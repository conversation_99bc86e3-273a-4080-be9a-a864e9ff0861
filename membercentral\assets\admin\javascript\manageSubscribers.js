var mcg_itemsingle = 'subscription tree'; var mcg_itemplural = 'subscription trees';
var initgrid = 0;

function loadSubscribersTab() {
	mca_setupDatePickerRangeFields('fTermStartFrom','fTermStartTo');
	mca_setupDatePickerRangeFields('fTermEndFrom','fTermEndTo');
	mca_setupDatePickerRangeFields('fOffrExpFrom','fOffrExpTo');
	mca_setupCalendarIcons('frmFilter');
	mca_setupSelect2();
	
    $('body').on('change', '#selfSubType', function(e) {
		mca_callChainedSelect('selfSubType','selfSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
		$('#fSubType').val($('#selfSubType').val());
		$('#fSubscription, #fRate').val('0');
		$('#selfRate').empty().trigger('change');
    });	

	$(document).on('change', '#fSubStatus', function(e) {
		if($(this).val() == 'O'){
			$('.offrExpWrap').removeClass('d-none');
		}else{
			$('.offrExpWrap input').val('');
			$('.offrExpWrap').addClass('d-none');
		}
    });	

    $('body').on('change', '#selfSubscription', function(e) {
		mca_callChainedSelect('selfSubscription','selfRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
		$('#fSubscription').val($('#selfSubscription').val());
		$('#fRate').val('0');
    });				

    $('body').on('change', '#selfRate', function(e) {
		var r = $('#selfRate').val() || '';
		if (r.length > 0) { $('#fRate').val(r.toString()); } else { $('#fRate').val(''); }
    });	

	if ($('#associatedMemberID').val() == 0 && $('#associatedGroupID').val() == 0) $("#divAssociatedVal").hide();

	$(".assocType").on("click",function(){	
		var assocType = $('input:radio[name=assocType]:checked').val();
		if (assocType != undefined) {
			if (assocType == "group") selectGroupInvFilter();
			else selectMemberInvFilter();
		}
	});

	$("#aClearAssocType").on("click",function() {
		$(".assocType").each(function(){
		     $(this).attr("checked",false);
		});	
		$('#associatedVal').html("");
		$('#associatedMemberID').val(0);
		$('#associatedGroupID').val(0);
		$('#associatedMemberName').val('');
		$('#associatedMemberNum').val('');
		$('#associatedGroupName').val('');
		$('#divAssociatedVal').hide();
		$("#expandSearch").hide();
	});
}


function sortGrid(columnIndex,sortType,sortDirection){	
	var checkedList = mcg_g.getCheckedRows(0);
	mcg_g.clearAll();
	mcg_g.loadXML(mcg_gridQString+(mcg_gridQString.indexOf("?")>=0?"&":"?")+"orderby="+columnIndex+"&direct="+sortDirection+"&checkedList="+checkedList);
	mcg_g.setSortImgState(true,columnIndex,sortDirection);
	return false;
}
function selectGroupInvFilter() {
	var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=associatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Subscribers by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Subscribers by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function selectMemberInvFilter() {		
	var selhref = link_memSelectGotoLink+'&mode=direct&fldName=associatedMemberID&retFunction=top.updateField&dispTitle=';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Subscribers by Member',
		iframe: true,
		contenturl: selhref,
		strmodalfooter : {
			classlist: 'd-none',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '',
			extrabuttonlabel: 'Submit',
		}
	});
}
function updateField(fldID, mID, mNum, mName) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		$('#associatedMemberName').val(mName);
		$('#associatedMemberNum').val(mNum);
		fldName.html(mName + ' (' + mNum + ')');
		$('#expandSearch').show();
		$('#associatedGroupID').val(0);
		$('#divAssociatedVal').show();
	} else {
		fldName.html('');
		$('#expandSearch').hide();
		$('#divAssociatedVal').hide();
	}
}
function updateGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');		
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");	
		$('#associatedGroupName').val(newgPath);
		fldName.html(newgPath);
		$('#associatedMemberID').val(0);
		$('#divAssociatedVal').show();
		$('#expandSearch').hide();
	} else {
		fldName.html('');
		$('#divAssociatedVal').hide();
		$('#expandSearch').hide();
	}
}
function clearFilterSubGrid() {
	/* since reset() won't clear fields with default values */
	$('#frmFilter input[type="hidden"], #frmFilter input[type="text"], #fHasCardOnFile').val('');
	$('#fChkAll, #fSubPaymentStatus, #fFreq, #selfSubType, #selfSubscription, #fRevenueGL').val(0);
	$('#fSubStatus').val('A');
	$('#selfSubType, #selfSubscription').trigger('change');
	$('#selfRate').empty().trigger('change');
	$('#aClearAssocType').click();
	filterSubGrid();
}
function filterSubGrid() {
	var saveFilterResult = function(r) { 
		if ((((sID == "R") && (statusStart != "R")) || ((sID != "R") && (statusStart == "R"))) ||
				(((sID == "A") && (statusStart != "A")) || ((sID != "A") && (statusStart == "A"))) ||
				(((sID == "P") && (statusStart != "P")) || ((sID != "P") && (statusStart == "P"))) ||
				(((sID == "I") && (statusStart != "I")) || ((sID != "I") && (statusStart == "I"))) ||
				(((sID == "E") && (statusStart != "E")) || ((sID != "E") && (statusStart == "E"))) ||
				(((sID == "O") && (statusStart != "O")) || ((sID != "O") && (statusStart == "O"))))
		{
			document.location.href = link_listReports + '&chkAll='+fChkAll+'&ig=1';
		} else {
			mcg_gridQString = subsXML + '&stID=' + stID + '&subID=' + subID + '&freqID=' + freqID +  '&rateID=' + rateID + '&dtTSf=' + dtTSf + '&dtTSt=' + dtTSt + '&dtTEf=' + dtTEf +'&dtOffrExpF=' + dtOffrExpF + '&dtOffrExpT=' + dtOffrExpT + '&dtTEt=' + dtTEt + '&sID=' + sID + '&spID=' + spID + '&fCard=' + hasCard + '&chkAll=' + fChkAll + '&associatedMemberID='+associatedMemberID+'&associatedGroupID='+associatedGroupID+'&fRevGL='+RevGL+'&linkedRecords='+linkedRecords;
			if (initgrid==0) {
				mcg_init();
				initgrid=1;
				$('#divLegend, #divSubBtnBar').show();
			} else mcg_reloadGrid();
		}
	};
	
	var sID = $('#fSubStatus').val();
	var stID = $('#fSubType').val();
	if (stID.length == 0) stID = '0';
	var subID = $('#fSubscription').val();
	if (subID.length == 0) subID = '0';
	var freqID = $('#fFreq').val();
	var dtTSf = $('#fTermStartFrom').val();
	var dtTSt = $('#fTermStartTo').val();
	var dtTEf = $('#fTermEndFrom').val();
	var dtTEt = $('#fTermEndTo').val();
	var dtOffrExpF = $('#fOffrExpFrom').val();
	var dtOffrExpT = $('#fOffrExpTo').val();
	var spID = $('#fSubPaymentStatus').val();
	var hasCard = $('#fHasCardOnFile').val();
	var RevGL = $('#fRevenueGL').val();
	var associatedMemberID = $('#associatedMemberID').val();
	var linkedRecords = $('input[name="linkedRecords"]:checked').val();
	var associatedGroupID = $('#associatedGroupID').val();
	var associatedMemberName = $('#associatedMemberName').val();
	var associatedMemberNum = $('#associatedMemberNum').val();
	var associatedGroupName = $('#associatedGroupName').val();
	var rateID = $('#fRate').val();
	if (rateID.length == 0)	rateID = '0';
	var fChkAll = $('#fChkAll').val();

	var objParams = { fType:'list',spID:spID,stID:stID,subID:subID,rateID:rateID,freqID:freqID,dtTSf:dtTSf,dtTSt:dtTSt,dtTEf:dtTEf,dtTEt:dtTEt,
		sID:sID,fCard:hasCard,fRevGL:RevGL,associatedMemberID:associatedMemberID,associatedGroupID:associatedGroupID,
		associatedMemberName:associatedMemberName,associatedMemberNum:associatedMemberNum,associatedGroupName:associatedGroupName,
		linkedRecords:linkedRecords,dtOffrExpF:dtOffrExpF,dtOffrExpT:dtOffrExpT };
	TS_AJX('ADMSUBS','saveSubReportFilter',objParams,saveFilterResult,saveFilterResult,20000,saveFilterResult);
}
function quickSelectSubRates(elmID, isRenewalRate){
	let arrRateIDs = $('select#' + elmID).find('option[data-isrenewalrate="'+isRenewalRate+'"]').map(function(index,element){ 
			return $(element).attr("value");
		}).toArray();
	$('select#' + elmID).val(arrRateIDs).trigger('change');
}
function toggleATRow(rowID) {
	var st = $('#atChanges_' + rowID).css("display");
	if (st == 'none') {
		$('#atChanges_' + rowID).show();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-open.jpg");
	} else {
		$('#atChanges_' + rowID).hide();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-closed.jpg");
	}
}
function toggleATGrid() {
	var st = $('#mcg_gridboxAT').css("display");
	if (st == 'none') {
		$('#mcg_gridboxAT').show();
		$('#auditResultsShow').hide();
		$('#auditResultsHide').show();
	} else {
		$('#mcg_gridboxAT').hide();
		$('#auditResultsShow').show();
		$('#auditResultsHide').hide();
	}
}

function startExportSubs() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Export Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: link_exportSubs + '&' + $('#frmFilter').serialize(),
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto d-none',
				extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnContinue").click',
				extrabuttonlabel: 'Continue',
			}
		});
	}
}
function exportStandard() {	top.closeBox(); }
function exportMailhouse() { top.closeBox(); }

function setCheckedSubs() {
	var strSubscribers = mcg_g.getCheckedRows(0);
	var checkedAll = document.forms['frmFilter'].fChkAll.value;

	if (checkedAll == 1) {
		var strUncheck = '';
		mcg_g.forEachRow(function(id){
			var cell = mcg_g.cells(id,0);
			if (cell.isCheckbox() && !cell.isChecked()) { 
				if (strUncheck.length != 0) strUncheck += ',';
				strUncheck += id;
			}
		});
		document.forms['frmFilter'].fChkedSubs.value = '';
		document.forms['frmFilter'].fUnchkedSubs.value = strUncheck;
	} else {
		document.forms['frmFilter'].fChkedSubs.value = strSubscribers;
		document.forms['frmFilter'].fUnchkedSubs.value = '';
	}
}
function checkMarkExpired() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Expire Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startMarkExpired);
	}	
}

function startGenSub() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Generate Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startGenerateSub);
	}
}

function massUpdateGraceEndDate() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Change Subscription Grace End Date';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startUpdateGraceEndDate);
	}
}

function checkMarkInactive() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Inactivate Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startMarkInactive);
	}
}

function checkMarkActive() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Activating Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startMarkActive);
	}
}

function checkMarkBilled() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Billing Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startMarkBilled);
	}
}

function startAddSub(subupd) {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Add Subscription';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startForceAddSub+'&subupd=' + subupd);
	}
}

function deleteRenewals() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Deleting Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startDeleteRenewals);
	}
}

function removePaymentMethods() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Remove Pay Method from Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_removePaymethod);
	}
}

function removeAddOn() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Remove AddOn Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startRemoveAddons);
	}
}

function checkSendOffers() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Select Email Template';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		var sendOffersLink = link_startGenerateOffers + '&' + $('#frmFilter').serialize();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: sendOffersLink,
			strmodalfooter: {
				classlist: 'd-flex justify-content-between',
				showclose: false,
				buttons: [
					{
						class:"btn btn-sm btn-secondary d-none",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnChangeTemplate").click',
						label: 'Change Template', 
						name: 'btnChangeTemplate',
						id: 'btnChangeTemplate'
					},
					{
						class:"btn btn-sm btn-secondary d-none mr-auto",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnTestTemplate").click',
						label: 'Send Test E-mail', 
						name: 'btnTestTemplate',
						id: 'btnTestTemplate'
					},
					{
						class:"btn btn-sm btn-primary ml-auto",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSelectTemplate").click',
						label: 'Select Template', 
						name: 'btnSelectTemplate',
						id: 'btnSelectTemplate'
					},
					{
						class: "btn btn-primary btn-sm ml-auto d-none",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnProcessTemplate").click',
						label: 'Send Emails', 
						name: 'btnProcessTemplate',
						id: 'btnProcessTemplate'
					}
				]
			}
		});
	}
}

function checkMarkAccepted() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Should Subscribers Receive Email Notifications?';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		if (confirm("Are you sure you want to accept the selected subscription trees?")) {
			setCheckedSubs();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: title,
				iframe: true,
				contenturl: link_startMarkAccepted + '&' + $('#frmFilter').serialize(),
				strmodalbody:{
					classlist:'mcModalBodyCustom'
				},
				strmodalfooter: {
					classlist: '',
					showclose: false,
					buttons: [
						{
							class:"btn btn-sm btn-primary btnSubAction",
							clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSend").click',
							label: 'Send Emails', 
							name: 'btnSend',
							id: 'btnSend'
						},
						{
							class: "btn btn-sm btn-primary btnSubAction",
							clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSkip").click',
							label: 'Skip Emails', 
							name: 'btnSkip',
							id: 'btnSkip'
						}
					]
				}
			});
		}
	}
}

function changeOfferExpirationDate() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Change Subscription Offer Expiration Date';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		setCheckedSubs();
		showSubscriptionModal(title, link_startUpdateOfferExpirationDate);
	}
}

function checkAllSubscribers(chk) {
	document.forms['frmFilter'].fChkAll.value = chk ? 1 : 0;
	filterSubGrid();
}
function setSelectedSubCountDisplay(c){
	$('#selSubCountDisp').data('selcount', c);
	$('#selSubCountDisp').html(' / <b>' + c + '</b> ' + (c == 1 ? mcg_itemsingle : mcg_itemplural) + ' selected').show();
}
function getUncheckedSubscribersArray(){
	var arrUnchkedSubs = [];
	mcg_g.forEachRow(function(id){
		var cell = mcg_g.cells(id,0);
		if (cell.isCheckbox() && !cell.isChecked()) { 
			arrUnchkedSubs.push(id);
		}
	});
	return arrUnchkedSubs;
}
function onCheckSubscriberEntry(){
	if(mcg_g.getRowsNum() == 0) return false;
	var displayCount = 0;
	if ($('#masterCheckBox').is(':checked')) {
		var uncheckedSubArray = getUncheckedSubscribersArray();
		displayCount = mcg_g.getRowsNum() - uncheckedSubArray.length;
	} else {
		var selectedIds = mcg_g.getCheckedRows(0);
		displayCount = selectedIds.length ? selectedIds.split(',').length : 0;
	}
	setSelectedSubCountDisplay(displayCount);	
}
function onXLSSubscribers(){
	$('#selSubCountDisp').data('selcount', 0);
	$('#selSubCountDisp').hide();
}
function onXLESubscribers(){
	onCheckSubscriberEntry();
}
function existsSubsOnFilteredGrid() {
	if(mcg_g.getRowsNum() == 0 || $('#selSubCountDisp').data('selcount') == 0) {
		return 'No subscriptions were selected.';
	} else {
		return '';
	}
}
function showSubRenewalLink(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Subscription Renewal Link and Code',
		iframe: true,
		contenturl: link_subRenewal+'&sid='+sid
	});
}
function showSubscriptionWarningModal(title, message) {
	MCModalUtils.showModal({
		verticallycentered: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false,
		},
		size: 'md',
		title: title,
		strmodalbody: {
			content: '<div class="alert alert-warning">' + message + '</div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
		}
	});
}
function showSubscriptionModal(title, link) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: title,
		iframe: true,
		contenturl: link + '&' + $('#frmFilter').serialize()
	});
}