<cfcomponent output="no">

	<cffunction name="doesAssociationHandlesPayment" access="public" output="false" returntype="boolean">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select participantID
			from dbo.tblParticipants
			where orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#">
			and handlesOwnPayment = 1;
		</cfquery>

		<cfreturn qryCheck.recordCount is 1>
	</cffunction>

	<cffunction name="getSWLinkDetails" access="public" output="false" returntype="query">
		<cfargument name="linkID" type="numeric" required="yes">

		<cfset var qryLink = "">
		
		<cfstoredproc procedure="sw_getLink" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkID#">
			<cfprocresult name="qryLink" resultset="1">
		</cfstoredproc>
		
		<cfreturn qryLink>
	</cffunction>

	<cffunction name="deleteProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="deleteProgram")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryTitles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				set nocount on;

				declare @seminarID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;

				select t.titleID
				from dbo.tblTitles as t
				inner join dbo.tblSeminarsAndTitles as sat on sat.titleID = t.titleID
				left outer join dbo.tblSeminarsAndTitles as satExc on satExc.titleID = t.titleID
					and satExc.seminarID <> @seminarID
				where sat.seminarID = @seminarID
				and t.isDeleted = 0
				and satExc.seminarTitleID is null;
			</cfquery>

			<cfif local.qryTitles.recordCount>
				<cfset local.objSWTitlesAdmin = CreateObject("component","seminarWebSWTL")>
				<cfloop query="local.qryTitles">
					<cfset local.objSWTitlesAdmin.deleteTitle(mcproxy_siteID=arguments.mcproxy_siteID, mcproxy_siteCode=arguments.mcproxy_siteCode, titleId=local.qryTitles.titleID)>
				</cfloop>
			</cfif>

			<!--- check if there is a connected Zoom Webinar --->
			<cfif arguments.programType eq "SWL">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWLProgram">
					select swl.ZoomWebinarID
					from dbo.tblSeminars as s
					inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = s.seminarID
					where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					and s.isDeleted = 0
				</cfquery>

				<cfif local.qrySWLProgram.recordCount AND len(local.qrySWLProgram.ZoomWebinarID)>
					<cfset createObject("component","seminarWebSWL").disconnectSWLSeminarFromZoomWebinar(mcproxy_siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, webinarID=local.qrySWLProgram.ZoomWebinarID)>
				</cfif>
			</cfif>

			<cfstoredproc procedure="sw_deleteSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveProgramLink" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="linkID" type="numeric" required="yes">
		<cfargument name="linkURL" type="string" required="yes">
		<cfargument name="linkName" type="string" required="yes">
		<cfargument name="linkDesc" type="string" required="yes">
		<cfargument name="purchaseURL" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_saveProgramLink" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkURL#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkDesc#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.purchaseURL#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeProgramLink" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="linkID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfif arguments.programType EQ "SWOD">
				<cfstoredproc procedure="sw_removeSeminarAndLink" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="sendPaymentReceipt" access="public" output="false" returntype="struct">
		<cfargument name="receiptUUID" type="string" required="true">
		<cfargument name="sendToEmail" type="string" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.strSWReceipts = application.mcCacheManager.sessionGetValue(keyname='strSWReceipts',defaultValue={})>
			<cfif isStruct(local.strSWReceipts) and structKeyExists(local.strSWReceipts,arguments.receiptUUID) and isValid("regex",arguments.sendToEmail,application.regEx.email)>
				<cfset local.strReceipt = duplicate(local.strSWReceipts[arguments.receiptUUID])>
				
				<!--- resend payment receipt --->
				<cfif structKeyExists(local.strReceipt,"recipientID")>
					<cfset CreateObject('component', 'model.admin.emailBlast.emailBlast').resendRecipientEmail(siteID=local.strReceipt.siteID, recipientID=local.strReceipt.recipientID, toEmail=arguments.sendToEmail)>
					<cfset local.data.success = true>

				<!--- send new payment receipt --->
				<cfelse>
					<cfset local.seminarWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SemWebCatalog',siteID=local.strReceipt.siteID)>

					<cfset local.arrEmailTo = [{ name=local.strReceipt.purchaserName, email=arguments.sendToEmail }]>
					<cfif len(local.strReceipt.purchaserOverrideEmail) AND local.strReceipt.purchaserOverrideEmail NEQ arguments.sendToEmail>
						<cfset local.arrEmailTo.append({ name=local.strReceipt.purchaserName, email=local.strReceipt.purchaserOverrideEmail })>
					</cfif>

					<cfif isDefined("local.strReceipt.email")>
						<cfset local.streceiptEmail = local.strReceipt.email>
					<cfelseif isDefined("local.strReceipt.supportProviderEmail")>
						<cfset local.streceiptEmail = local.strReceipt.supportProviderEmail>
					</cfif>

					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=local.strReceipt.orgname, email=local.strReceipt.networkEmailFrom },
							emailto=local.arrEmailTo,
							emailreplyto=local.streceiptEmail,
							emailsubject="Registration Payment Receipt",
							emailtitle="#local.strReceipt.sitename# Payment Receipt",
							emailhtmlcontent=local.strReceipt.receiptData,
							emailAttachments=local.strReceipt.arrInvoicePaths,
							siteID=local.strReceipt.siteID,
							memberID=local.strReceipt.purchaserMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBPAY"),
							sendingSiteResourceID=local.seminarWebSiteResourceID
					)>
					<cfif NOT local.strResult.success>
						<cfthrow message="#local.strResult.err#">
					<cfelse>
						<cfset local.strSWReceipts[arguments.receiptUUID].messageID = local.strResult.messageID>
						<cfset local.strSWReceipts[arguments.receiptUUID].recipientID = local.strResult.arrRecipientID[1]>
						<cfset application.mcCacheManager.sessionSetValue(keyname='strSWReceipts', value=local.strSWReceipts)>
						<cfset local.data.success = true>
					</cfif>
				</cfif>
			<cfelse>
				<cfthrow message="Receipt not found." type="ReceiptNotFound">
			</cfif>
		<cfcatch type="ReceiptNotFound">
			<cfset local.data.success = false>
		</cfcatch>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="toggleAllowRegistrants" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="allowRegistrants" type="numeric" required="true">

		<cfset var local = StructNew()>
		<cfset local.data.success = false>
		<cfset local.data.errMsg = "">

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="toggleAllowRegistrants")>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_toggleAllowRegistrants">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.allowRegistrants#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="toggleActivateExam" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="responseID" type="numeric" required="yes">
		<cfargument name="isActive" type="boolean" required="yes">
		
		<cfset var local = StructNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
		<cfset local.qryEnrollmentInfo = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentByEnrollmentID(enrollmentID=arguments.enrollmentID)>

		<cfif local.qryEnrollmentInfo.format eq "SWL">
			<cfset local.hasRights = local.tmpRights.manageSWLRegProgressAll is 1 OR (local.tmpRights.manageSWLRegProgressSignUp is 1 AND local.qryEnrollmentInfo.signupOrgCode EQ arguments.mcproxy_siteCode)>
		<cfelseif local.qryEnrollmentInfo.format eq "SWOD">
			<cfset local.hasRights = local.tmpRights.manageSWODRegResponseAll is 1 OR (local.tmpRights.manageSWODRegResponseSignUp is 1 AND local.qryEnrollmentInfo.signupOrgCode EQ arguments.mcproxy_siteCode)>
		</cfif>

		<cftry>
			<cfif not local.hasRights>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_toggleActivateExam">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.responseID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.isActive#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<!--- SEMINAR RATES --->

	<cffunction name="getSeminarRatesBySeminarID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">

		<cfset var qryRates = "">

		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getSeminarRatesBySeminarID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocresult name="qryRates">
		</cfstoredproc>

		<cfreturn qryRates>
	</cffunction>

	<cffunction name="getSeminarRateByRateID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfset var qryRate = "">
		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getSeminarRateByRateIDForAdmin">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			<cfprocresult name="qryRate">
		</cfstoredproc>
		<cfreturn qryRate>
	</cffunction>

	<cffunction name="saveSeminarRate" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">
		<cfargument name="rate" type="string" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">
		<cfargument name="newRateGrouping" type="string" required="yes">
		<cfargument name="parentRateID" type="numeric" required="yes">
		<cfargument name="copyPerms" type="boolean" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qrySaveSWRate = "">

		<cfquery name="qrySaveSWRate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @rateID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">,0),
					@participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">,
					@seminarID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">,
					@rateGroupingID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">,0),
					@rateName varchar(100) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateName#">,
					@rate money = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#">,
					@isHidden bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isHidden#">,
					@revenueGLAccountID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">,0),
					@rateGrouping varchar(200) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.newRateGrouping)#">,''),
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@siteResourceID int,
					@parentRateID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.parentRateID#">,0),
					@copyPerms bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.copyPerms#">,
					@siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
					@minRateId int,@srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_include bit, 
					@srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int,@parentsiteResourceID int;	

				IF @rateGroupingID IS NULL AND @rateGrouping IS NOT NULL 
					SELECT @rateGroupingID = rateGroupingID
					FROM dbo.tblSeminarsAndRatesGrouping
					WHERE participantID = @participantID
					AND seminarID = @seminarID
					AND rateGrouping = @rateGrouping;

				BEGIN TRAN;
					IF @rateGroupingID IS NULL AND @rateGrouping IS NOT NULL 
						EXEC dbo.sw_createSeminarRateGrouping @participantID=@participantID, @seminarID=@seminarID, @rateGrouping=@rateGrouping,
							@rateGroupingID=@rateGroupingID OUTPUT;

					IF @rateID IS NULL
						EXEC dbo.sw_createSeminarRate @participantID=@participantID, @seminarID=@seminarID, @rateGroupingID=@rateGroupingID,
							@rateName=@rateName, @rate=@rate, @isHidden=@isHidden, @revenueGLAccountID=@revenueGLAccountID, 
							@recordedByMemberID=@recordedByMemberID, @rateID=@rateID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

						IF @parentRateID > 0 AND @copyPerms = 1 BEGIN 
							select @minRateId = min(r.rateID) 
							from dbo.tblSeminarsAndRates as r
							inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
							where r.rateID =@parentRateID
							and sr.siteResourceStatusID = 1;

							WHILE @minRateID is not null BEGIN
								select @parentsiteResourceID=siteResourceID
								from dbo.tblSeminarsAndRates as r
								where rateID = @minRateID;

								-- copy resource rights for this resource		
								SET @srr_rightsID = null;
								SELECT @srr_rightsID = min(resourceRightsID) from membercentral.dbo.cms_siteResourceRights where resourceID = @parentsiteResourceID and siteID = @siteID;
								WHILE @srr_rightsID IS NOT NULL BEGIN
									SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
										@srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
										@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
									FROM membercentral.dbo.cms_siteResourceRights
									WHERE resourceRightsID = @srr_rightsID and siteID = @siteID;

									EXEC membercentral.dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=@srr_include, 
										@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID, 
										@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;
				
									SELECT @srr_rightsID = min(resourceRightsID) from membercentral.dbo.cms_siteResourceRights where resourceID = @parentsiteResourceID and siteID = @siteID and resourceRightsID > @srr_rightsID;
								END

								select @minRateId = min(rateID) 
								from dbo.tblSeminarsAndRates as r
								inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
								where r.rateID =@parentRateID
								and sr.siteResourceStatusID = 1
								and rateID > @minRateID;
							END
						END 
					ELSE
						EXEC dbo.sw_updateSeminarRate @participantID=@participantID, @rateID=@rateID, @rateGroupingID=@rateGroupingID,
							@rateName=@rateName, @rate=@rate, @isHidden=@isHidden, @revenueGLAccountID=@revenueGLAccountID, 
							@recordedByMemberID=@recordedByMemberID;
				COMMIT TRAN;

				SELECT @rateID AS rateID;

			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "rateID":val(local.qrySaveSWRate.rateID) }>
	</cffunction>
	
	<cffunction name="deleteSeminarRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_deleteSeminarRate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteMemberGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="rateid" type="numeric" required="true">
		<cfargument name="groupid" type="numeric" required="true">
		<cfargument name="include" type="numeric" required="false" default="1">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySiteResourceRights" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @siteResourceID int;

				select @siteResourceID = siteResourceID
				from seminarWeb.dbo.tblSeminarsAndRates
				where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateid#">
				and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

				select srr.resourceID, srr.resourceRightsID, sr.siteID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID
				where srr.resourceID = @siteResourceID
				and srr.groupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.groupID#">
				and srr.include = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.include#">
			</cfquery>

			<cfloop query="local.qrySiteResourceRights">
				<cfstoredproc procedure="cms_deleteSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.siteID#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.resourceID#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.resourceRightsID#">
				</cfstoredproc>		
			</cfloop>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
			
	<!--- SEMINAR RATES GROUPING --->

	<cffunction name="getSeminarRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data.success = true>
		
		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryRateGrouping">
				SELECT rg.rateGroupingID, rg.rateGrouping, rg.seminarID, rg.rateGroupingOrder
				FROM dbo.tblSeminarsAndRatesGrouping rg
				WHERE rg.rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">
				AND rg.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			</cfquery>
			<cfset local.data.rateGroupingID = local.qryRateGrouping.rateGroupingID>
			<cfset local.data.rateGrouping = local.qryRateGrouping.rateGrouping>
		<cfcatch type="any">		
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSeminarRateGroupsbySeminarID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfset var qryRateGroupings = structNew()>
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="qryRateGroupings">
			SET NOCOUNT ON;

			DECLARE @participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			SELECT rg.rateGroupingID, rg.rateGrouping, rg.seminarID, rg.rateGroupingOrder,
				(
					SELECT COUNT(r.rateID) 
					FROM dbo.tblSeminarsAndRates as r
					inner join memberCentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
					WHERE r.rateGroupingID = rg.rateGroupingID
					AND r.participantID = @participantID
				) AS rateCount
			FROM dbo.tblSeminarsAndRatesGrouping rg
			WHERE rg.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			AND rg.participantID = @participantID
			ORDER BY rg.rateGroupingOrder
		</cfquery>
		<cfreturn qryRateGroupings>
	</cffunction>

	<cffunction name="saveSeminarRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="rateGrouping" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.updateSeminarRateGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @participantID int, @rateGroupingID INT, @rateGrouping VARCHAR(200), @seminarID INT;
					SET @participantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.participantID#">;
					SET @rateGroupingID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateGroupingID#">;
					SET @rateGrouping = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateGrouping#">;
					
					SELECT @seminarID = seminarID 
					FROM dbo.tblSeminarsAndRatesGrouping 
					WHERE rateGroupingID = @rateGroupingID
					AND participantID = @participantID;
					
					IF EXISTS (
						SELECT rateGroupingID FROM dbo.tblSeminarsAndRatesGrouping 
						WHERE seminarID = @seminarID 
						AND rateGrouping = @rateGrouping 
						AND rateGroupingID <> @rateGroupingID
						AND participantID = @participantID
					)
						RAISERROR('Duplicate Grouping Name.',16,1);

					UPDATE dbo.tblSeminarsAndRatesGrouping
					SET rateGrouping = @rateGrouping
					WHERE rateGroupingID = @rateGroupingID
					AND participantID = @participantID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfif findNoCase("Duplicate Grouping Name",cfcatch.detail)>
				<cfset local.data.errmsg = "Duplicate Grouping Name.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="deleteSeminarRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryDeleteRateGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">,
						@seminarID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">,
						@rateGroupingID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">;
				
					BEGIN TRAN;
						UPDATE dbo.tblSeminarsAndRates
						SET rateGroupingID = NULL
						WHERE rateGroupingID = @rateGroupingID
						AND participantID = @participantID;

						DELETE FROM dbo.tblSeminarsAndRatesGrouping
						WHERE rateGroupingID = @rateGroupingID
						AND participantID = @participantID;

						EXEC dbo.sw_reorderSeminarRateGrouping @participantID=@participantID, @seminarID=@seminarID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doSeminarRateGroupingMove" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_moveSeminarRateGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="copyRatesFromSWProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="copyFromProgramID" type="numeric" required="true">
		<cfargument name="copyToProgramID" type="numeric" required="true">
		<cfargument name="ft" type="string" required="true">		

		<cfset var local = structNew()>
		<cfset local.data.success = false>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.ft, programID=arguments.copyToProgramID, action="ManageRateChanges", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfif listFindNoCase("SWL,SWOD", arguments.ft)>
				<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_copyRates">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyFromProgramID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyToProgramID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
			<cfelseif arguments.ft EQ "SWB">
				<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="swb_copyRates">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyFromProgramID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.copyToProgramID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>	
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>	
	</cffunction>

	<cffunction name="getSeminarRatesForSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="copyFromProgramID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrrates':arrayNew(1) }>
		<cfset local.qualifySWProgramRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SWProgramRate", functionName="qualify")>

		<cfquery name="local.qryRates" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @rfid INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySWProgramRateRFID#">;
			DECLARE @participantID int = <cfqueryparam value="#arguments.participantID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @seminarID int = <cfqueryparam value="#arguments.copyFromProgramID#" cfsqltype="CF_SQL_INTEGER">;

			WITH RateGroupings AS (
				SELECT rateGroupingID, rateGroupingOrder, rateGrouping
				FROM dbo.tblSeminarsAndRatesGrouping
				WHERE seminarID = @seminarID
				AND participantID = @participantID
					UNION ALL
				SELECT 0, 0, 'Default - No Grouping'
			)
			SELECT r.rateName, r.rate, STRING_AGG(case when srrc.include=1 then g.groupID end,'|') AS groupIDs
				,STRING_AGG(case when srrc.include=0 then g.groupID end,'|') as deniedGroupIDs
				,replace(STRING_AGG(replace(case when srrc.include=0 then 'Denied: '+g.groupPathExpanded else g.groupPathExpanded end,'|',char(7)),'^--^'),char(7),'|') as groupPathsList
				,ROW_NUMBER() OVER (ORDER BY rg.rateGroupingOrder) as row
			FROM RateGroupings AS rg
			LEFT OUTER JOIN dbo.tblSeminarsAndRates AS r 
				INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
				LEFT OUTER JOIN memberCentral.dbo.cms_siteResourceRightsCache AS srrc
					INNER JOIN memberCentral.dbo.ams_groups AS g ON g.groupID = srrc.groupID
					ON srrc.resourceid = r.siteresourceID
					AND srrc.functionID = @rfid 
					AND g.status = 'A'
					AND srrc.siteID = sr.siteID
				ON isnull(r.rateGroupingID,0) = rg.rateGroupingID
				AND r.seminarID = @seminarID
				AND r.rate >= 0
				AND r.participantID = @participantID
			group by rateName, r.rate, rg.rateGroupingOrder, r.rateOrder
			ORDER BY rg.rateGroupingOrder, r.rateOrder;
		</cfquery>

		<cfset local.hasRateGroup = 0>

		<cfloop query="local.qryRates">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['autoid'] = local.qryRates.row>
			<cfset local.tmp['ratename'] = local.qryRates.rateName>
			<cfset local.tmp['rate'] = local.qryRates.rate>
			<cfset local.tmp['groupids'] = local.qryRates.groupIDs>
			<cfset local.tmp['deniedgroupids'] = local.qryRates.deniedGroupIDs>
			<cfif listLen(local.qryRates.groupPathsList,'^--^') AND local.hasRateGroup eq 0>
				<cfset local.hasRateGroup = 1>
			</cfif>
			<cfset local.tmp['arrgroupnames'] = listToArray(replaceNoCase(local.qryRates.groupPathsList, "&amp;", "&", "all"),"^--^",false,true)>
			<cfset arrayAppend(local.returnStruct['arrrates'],local.tmp)>
		</cfloop>

		<cfset local.returnStruct['hasRateGroup'] = local.hasRateGroup>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveLearningObjective" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="objectiveID" type="numeric" required="yes">
		<cfargument name="objective" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = { success=false, objectiveid=arguments.objectiveID }>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfif arguments.objectiveID eq 0>
				<cfstoredproc procedure="sw_createLearningObjective" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.objective#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.data.objectiveid">
				</cfstoredproc>
			<cfelse>
				<cfstoredproc procedure="sw_updateLearningObjective" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.objectiveID#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.objective#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
			</cfif>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeLearningObjective" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="objectiveID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_removeLearningObjective" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.objectiveID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="toggleFeaturedProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="isFeatured" type="boolean" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="sw_toggleFeaturedProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isFeatured#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="toggleFeeExempt" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="isFeeExempt" type="boolean" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif not local.tmpRights.isFeeExempt>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_toggleEnrollmentFeeExemption" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isFeeExempt#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getProgramVideoPreviews" access="public" output="false" returntype="struct">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = { "success":true, "arrprogramvideopreviews":[] }>

		<cfstoredproc procedure="sw_getVideoPreviewsByProgramID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocresult name="local.qryProgramVideoPreviews" resultset="1">
		</cfstoredproc>

		<cfquery name="local.data.arrprogramvideopreviews" dbtype="query" returntype="array">
			select fileid, filename, filetitle, filedesc, filetype, previewid, timecodestart, timecodeend, isonline
			from [local].qryProgramVideoPreviews
		</cfquery>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveVideoPreview" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="startTimeCode" type="string" required="yes">
		<cfargument name="endTimeCode" type="string" required="yes">

		<cfset var local = structNew()>

		<!--- validate start and end time codes --->
		<cfset local.startTime = "#DateFormat(now(),"yyyy-mm-dd")# #arguments.startTimeCode#">
		<cfset local.endTime = "#DateFormat(now(),"yyyy-mm-dd")# #arguments.endTimeCode#">
		<cfif Datediff("s",local.startTime,local.endTime) lte 0>
			<cfthrow message="Invalid Start/End Time Codes">
		</cfif>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="manageVideoPreview", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_createVideoPreview" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.startTimeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.endTimeCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.data.previewID">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteVideoPreview" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="previewID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="manageVideoPreview", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_deleteVideoPreview" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.previewID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="isSeminarLocked" access="public" output="false" returntype="boolean">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var qrySeminar = "">

		<cfquery name="qrySeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select lockSettings
			from dbo.tblSeminars
			where seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
		</cfquery>

		<cfreturn qrySeminar.lockSettings is 1>
	</cffunction>

	<cffunction name="isProgramLocked" access="public" output="false" returntype="boolean">
		<cfargument name="titleID" type="numeric" required="yes">

		<cfset var qryLockSettings = "">

		<cfquery name="qryLockSettings" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			set nocount on;

			declare @titleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">;

			SELECT s.lockSettings AS lockSettings
			FROM dbo.tblSeminars AS s 
			INNER JOIN dbo.tblSeminarsAndTitles AS sat ON sat.seminarID = s.seminarID
			inner join dbo.tblTitles as t on sat.titleID = t.titleID
			where t.titleID = @titleID
		</cfquery>

		<cfreturn val(qryLockSettings.lockSettings) gt 0>
	</cffunction>

	<cffunction name="isBundleProgramLocked" access="public" output="false" returntype="boolean">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var qryBundle = "">

		<cfquery name="qryBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select lockSettings
			from dbo.tblBundles
			where bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
		</cfquery>

		<cfreturn qryBundle.lockSettings is 1>
	</cffunction>

	<cffunction name="updateProgramLockSettings" access="public" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="lockSWProgramSettings" type="boolean" required="yes">

		<cfquery name="local.qryUpdateSWODProgramSettings" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			DECLARE @orgID INT, @siteID INT, @programID INT, @programType VARCHAR(5), @lockSettings BIT, @recordedByMemberID int, @isChanged BIT = 0;
			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			SET @programID = <cfqueryparam value="#arguments.programID#" cfsqltype="CF_SQL_INTEGER">;
			SET @programType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.programType#">;
			SET @lockSettings = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.lockSWProgramSettings#">;
			SET @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;

			<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
				IF EXISTS(SELECT 1 FROM dbo.tblSeminars WHERE seminarID = @programID AND lockSettings <> @lockSettings) BEGIN
					UPDATE dbo.tblSeminars SET lockSettings = @lockSettings WHERE seminarID = @programID;
					SET @isChanged = 1;
				END
			<cfelseif arguments.programType EQ "SWB">
				IF EXISTS(SELECT 1 FROM dbo.tblBundles WHERE bundleID = @programID AND lockSettings <> @lockSettings) BEGIN
					UPDATE dbo.tblBundles SET lockSettings = @lockSettings WHERE bundleID = @programID;
					SET @isChanged = 1;
				END
			</cfif>

			IF @isChanged = 1 BEGIN
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				VALUES('{ "c":"auditLog", "d": {
					"AUDITCODE":"SW",
					"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
					"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
					"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
					"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
					"MESSAGE":"Lock Program Settings for '+ @programType +'-' + CAST(@programID AS VARCHAR(10)) + ' has been '+ CASE WHEN @lockSettings = 1 THEN 'enabled' ELSE 'disabled' END +'." } }');
			END
		</cfquery>
	</cffunction>

	<cffunction name="optInSWNationalProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="seminarID" type="string" required="yes">
		<cfargument name="natProgramID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_optInProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.natProgramID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="optInSWSeminar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="orgCodeList" type="string" required="yes">

		<cfset var local = StructNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfloop list="#arguments.orgCodeList#" index="local.thisOrgCode">
				<cfstoredproc procedure="sw_optInSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.thisOrgCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="optOutSWSeminar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="orgCodeList" type="string" required="yes">

		<cfset var local = StructNew()>
		
		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfloop list="#arguments.orgCodeList#" index="local.thisOrgCode">
				<cfstoredproc procedure="sw_optOutSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.thisOrgCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getAvailableSWProgramOptInAndOuts" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>
		
			<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
			<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.mcproxy_siteCode)>
			<cfset local.qryAssociation = local.strAssociation.qryAssociation>

			<cfquery name="local.data.arrOptinsOptOuts" datasource="#application.dsn.tlasites_seminarweb.dsn#" returntype="array">
				SELECT DISTINCT p.participantID, p.orgcode, tla.Description, CASE WHEN soi.optinID IS NULL THEN 0 ELSE 1 END AS isAdded, count(e.enrollmentID) as isEnrollmentExist
				FROM dbo.tblParticipants AS p 
				INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
				INNER JOIN dbo.tblNationalProgramParticipants as npp on npp.participantID = p.participantID
				AND npp.programID IN (
					SELECT distinct npp.programID
					FROM dbo.tblNationalPrograms np
					INNER JOIN dbo.tblNationalProgramParticipants npp on npp.programID = np.programid AND npp.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryAssociation.participantID#">)
				LEFT OUTER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID AND soi.IsActive = 1 AND soi.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				LEFT JOIN dbo.tblEnrollments e 
					INNER JOIN tblUsers u ON e.userID = u.userID
					INNER JOIN trialsmith.dbo.depoMemberData md ON u.depoMemberDataID = md.depoMemberDataID AND (md.adminflag2 IS NULL OR md.adminflag2 <> 'Y')
					ON e.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#"> AND e.participantID = p.participantID AND e.isActive = 1 
				WHERE  p.handlesOwnPayment = 0
				AND  p.participantID != <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryAssociation.participantID#">
				<cfif arguments.programType EQ 'SWOD'>
					AND p.isSWOD = 1
				<cfelse>
					AND p.isSWL = 1
				</cfif>
				GROUP BY p.participantID, p.orgcode, tla.Description, CASE WHEN soi.optinID IS NULL THEN 0 ELSE 1 END 
				ORDER BY p.orgcode;
			</cfquery>

		<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSWProgramCredits" access="public" output="false" returntype="query">
		<cfargument name="seminarIDList" type="string" required="yes">

		<cfset var qryProgramCredits = "">

		<cfquery name="qryProgramCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @seminarIDList varchar(max);
			SET @seminarIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarIDList#">;

			SELECT sac.seminarID as programID, ca.code as authorityCode, ca.authorityName, 
				CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') AS creditValueAwarded,
				CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') AS creditType
			FROM dbo.tblSeminarsAndCredit AS sac 
			INNER JOIN membercentral.dbo.fn_IntListToTable(@seminarIDList,',') as s on s.listitem = sac.seminarID
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
			INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID 
			LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.listitem
			LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.listitem
			CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS CRDA(credittype)
			CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
			WHERE cstat.status IN ('Approved','Pending','Self-Submitting')
			AND (
				sswl.liveid is not null
				OR
				sswod.ondemandID is not null and getdate() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
				)
			AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
			AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)');

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryProgramCredits>
	</cffunction>

	<cffunction name="getUpcomingProgramsForInvitationEmail" access="public" output="false" returntype="array">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>

		<cfset local.qryPrograms = "">
		<cfset local.arrPrograms = arrayNew(1)>

		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.siteCode)>
		<cfset local.qryPlatformFeaturedImageSetup = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getPlatformFeaturedImagesSetup()>
		<cfset local.qryParticipantFeaturedImageSetup = local.strAssociation.qryParticipantFeaturedImageSetup>

		<cfsavecontent variable="local.defaultOrderBy">
			<cfoutput>
			tmp.isFeatured desc, 
			<cfif arguments.programType eq "SWL">
				tmp.dateStart asc,
			<cfelseif arguments.programType eq "SWOD">
				tmp.dateActivated desc,
			</cfif>
			tmp.programName asc
			</cfoutput>
		</cfsavecontent>

		<cfquery name="local.qryPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @participantID int, @programID int, @siteCode varchar(10), @nowDate datetime = GETDATE();

			SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strAssociation.qryAssociation.participantID#">;
			SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
			SET @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

			IF OBJECT_ID('tempdb..##tmpSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpSWPrograms;
			
			CREATE TABLE ##tmpSWPrograms (rowNum int identity(1,1), programID int, programName varchar(250), programSubTitle varchar(250), dateStart datetime,
				dateEnd datetime, wddxTimeZones varchar(max), featureImageSiteCode varchar(10), featureImageOrgCode varchar(10),
				featureImageID int, featureImageFileExtension varchar(10), featureImageSizeID int, isFeatured bit, isSWODBundle bit, dateActivated datetime);

			<cfif arguments.programType eq "SWL">
				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, dateStart, dateEnd, wddxTimeZones,
					featureImageSiteCode, featureImageOrgCode, featureImageID, featureImageFileExtension, featureImageSizeID,isFeatured)
				SELECT s.seminarID, s.seminarName, s.seminarSubTitle, swl.dateStart, swl.dateEnd,
					swl.wddxTimeZones, mcs.siteCode, o.orgCode, fiu.featureImageID, fics.fileExtension, ficus.featureImageSizeID,
					CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END
				FROM dbo.tblSeminars as s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = mcs.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.seminarID = s.seminarID
				INNER JOIN dbo.tblSeminarsSWLive AS swl on swl.seminarID = s.seminarID
				INNER JOIN dbo.swl_SeminarsInMyCatalog(@siteCode,'1-1-2005',DATEADD(yy,2,@nowDate)) sic on sic.seminarID = s.seminarID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = mcs.siteID
					AND ficu.referenceType = 'swProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = s.seminarID
					AND fiu.referenceType = 'swlProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = 'swProgramListings'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID
				WHERE s.seminarID <> @programID;
			<cfelseif arguments.programType eq "SWOD">
				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, featureImageSiteCode, featureImageOrgCode,
					featureImageID, featureImageFileExtension, featureImageSizeID, dateActivated, isFeatured)
				SELECT s.seminarID, s.seminarName, s.seminarSubTitle, mcs.siteCode, o.orgCode,
					fiu.featureImageID, fics.fileExtension, ficus.featureImageSizeID, swod.dateActivated,
					CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END
				FROM dbo.tblSeminars as s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = mcs.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.seminarID = s.seminarID
				INNER JOIN dbo.tblSeminarsSWOD AS swod on swod.seminarID = s.seminarID
				INNER JOIN dbo.swod_SeminarsInMyCatalog(@siteCode) sic on sic.seminarID = s.seminarID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = mcs.siteID
					AND ficu.referenceType = 'swProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = s.seminarID
					AND fiu.referenceType = 'swodProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = 'swProgramListings'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID
				WHERE s.seminarID <> @programID;
			</cfif>

			SELECT TOP 5 programID, programName, programSubTitle, dateStart, dateEnd, wddxTimeZones, featureImageSiteCode, featureImageOrgCode,
				featureImageID, featureImageFileExtension, featureImageSizeID, isFeatured
			FROM ##tmpSWPrograms AS tmp
			ORDER BY #local.defaultOrderBy#;

			IF OBJECT_ID('tempdb..##tmpSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpSWPrograms;
		</cfquery>

		<cfloop query="local.qryPrograms">
			<cfif arguments.programType eq "SWL">
				<cfset local.tmpStr = { "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName,
										"programSubTitle":local.qryPrograms.programSubTitle, "displayDate":"", "featuredImagePath":"", "programDetailPageLink":""
									}>

				<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(seminarWDDXTimeZones=local.qryPrograms.wddxTimeZones, orgWDDXTimeZones=local.strAssociation.qryAssociation.wddxTimeZones, ifErrStartTime=local.qryPrograms.dateStart, ifErrEndTime=local.qryPrograms.dateEnd)>

				<cfif dateDiff("d", local.parsedTime.StartDate, local.parsedTime.EndDate) gt 0>
					<cfset local.tmpStr.displayDate = "#DateFormat(local.parsedTime.StartDate,'ddd, mmm d')# - #DateFormat(local.parsedTime.EndDate,'ddd, mmm d')#">
				<cfelse>
					<cfset local.tmpStr.displayDate = DateFormat(local.parsedTime.StartDate,'ddd, mmmm d')>
				</cfif>

				<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWLive-#local.qryPrograms.programID#">
			<cfelseif arguments.programType eq "SWOD">
				<cfset local.tmpStr = { "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName, "programSubTitle":local.qryPrograms.programSubTitle, "featuredImagePath":"", "programDetailPageLink":""}>
				<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWLive-#local.qryPrograms.programID#">
			</cfif>

			<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
				<cfset local.programFeaturedImagesStr = getProgramFeaturedImagePathsForInvitationEmail(publisherOrgCode=local.qryPrograms.featureImageSiteCode, programType=arguments.programType,
					programFeatureImageID=val(local.qryPrograms.featureImageID), programFeatureImageSizeID=val(local.qryPrograms.featureImageSizeID),
					programFeatureImageFileExtension=local.qryPrograms.featureImageFileExtension, qryPlatformFeaturedImageSetup=local.qryPlatformFeaturedImageSetup,
					qryParticipantFeaturedImageSetup=local.qryParticipantFeaturedImageSetup, qrySWHostName=local.qrySWHostName)>
					
				<cfset local.tmpStr.featuredImagePath = local.programFeaturedImagesStr.featuredImagePath>
			</cfif>

			<cfset local.arrPrograms.append(local.tmpStr)>
		</cfloop>

		<cfreturn local.arrPrograms>
	</cffunction>

	<cffunction name="getSWCreditDisplayInfoForInvitationEmail" access="public" output="false" returntype="struct">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="qryProgramCredits" type="query" required="yes">

		<cfset var local = structnew()>
		<cfset local.strResult = { creditAuthorityCount = 0, dspCredits = "" }>

		<cfif arguments.qryProgramCredits.recordCount>
			<cfquery name="local.qryThisProgramCredits" dbtype="query">
				SELECT authorityCode, authorityName, creditValueAwarded, creditType
				FROM arguments.qryProgramCredits
				WHERE programID = #int(val(arguments.seminarID))#
				ORDER BY authorityCode, creditType
			</cfquery>
			
			<cfif local.qryThisProgramCredits.recordCount>
				<cfquery name="local.qryUptoCreditCounts" dbtype="query">
					SELECT authorityCode, sum(creditValueAwarded) AS uptoCredits
					FROM local.qryThisProgramCredits
					GROUP BY authorityCode
					ORDER BY uptoCredits DESC
				</cfquery>

				<cfset local.strResult.creditAuthorityCount = local.qryUptoCreditCounts.recordCount>
				<cfif local.qryUptoCreditCounts.recordCount is 1>
					<cfset local.strResult.dspCredits = local.qryThisProgramCredits.authorityName & ": ">
					<cfloop query="local.qryThisProgramCredits">
						<cfset local.strResult.dspCredits = local.strResult.dspCredits & "#local.qryThisProgramCredits.creditValueAwarded# #local.qryThisProgramCredits.creditType#, ">
					</cfloop>
					<cfset local.strResult.dspCredits = mid(local.strResult.dspCredits, 1, len(local.strResult.dspCredits) - 2)>
				<cfelse>
					<cfset local.strResult.dspCredits = valueList(local.qryUptoCreditCounts.authorityCode, ", ")>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="getProgramFeaturedImagePathsForInvitationEmail" access="public" output="false" returntype="struct">
		<cfargument name="publisherOrgCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="programFeatureImageID" type="numeric" required="yes">
		<cfargument name="programFeatureImageSizeID" type="numeric" required="yes">
		<cfargument name="programFeatureImageFileExtension" type="string" required="yes">
		<cfargument name="qryPlatformFeaturedImageSetup" type="query" required="yes">
		<cfargument name="qryParticipantFeaturedImageSetup" type="query" required="yes">
		<cfargument name="qrySWHostName" type="query" required="yes">

		<cfset var local = structnew()>
		<cfset local.strResult = { programLevelFeaturedImagePath = "", defaultFeaturedImagePath = "", featuredImagePath = "" }>

		<cfset local.publisherSiteInfo = application.objSiteInfo.getSiteInfo(arguments.publisherOrgCode)>

		<cfset local.programFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.publisherSiteInfo.orgcode)#/#LCASE(local.publisherSiteInfo.sitecode)#/featuredimages/thumbnails/">
		<cfset local.programFtdThumbImgRootPath = "#arguments.qrySWHostName.scheme#://#arguments.qrySWHostName.mainHostName#/userassets/#LCASE(local.publisherSiteInfo.orgcode)#/#LCASE(local.publisherSiteInfo.sitecode)#/featuredimages/thumbnails/">

		<cfset local.platformFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.qryPlatformFeaturedImageSetup.orgcode)#/#LCASE(arguments.qryPlatformFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
		<cfset local.platformFtdThumbImgRootPath = "#arguments.qrySWHostName.scheme#://#arguments.qrySWHostName.mainHostName#/userassets/#LCASE(arguments.qryPlatformFeaturedImageSetup.orgcode)#/#LCASE(arguments.qryPlatformFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
		<cfset local.siteDefaultFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.qryParticipantFeaturedImageSetup.orgcode)#/#LCASE(arguments.qryParticipantFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
		<cfset local.siteDefaultFtdThumbImgRootPath = "#arguments.qrySWHostName.scheme#://#arguments.qrySWHostName.mainHostName#/userassets/#LCASE(arguments.qryParticipantFeaturedImageSetup.orgcode)#/#LCASE(arguments.qryParticipantFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">

		<cfif arguments.programFeatureImageID gt 0 AND fileExists("#local.programFtdThumbImgFullRootPath##arguments.programFeatureImageID#-#arguments.programFeatureImageSizeID#.#arguments.programFeatureImageFileExtension#")>
			<cfset local.strResult.programLevelFeaturedImagePath = "#local.programFtdThumbImgRootPath##arguments.programFeatureImageID#-#arguments.programFeatureImageSizeID#.#arguments.programFeatureImageFileExtension#">
		</cfif>

		<cfswitch expression="#arguments.programType#">
			<cfcase value="SWL">
				<cfif val(arguments.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
				<cfelseif val(arguments.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.platformFtdThumbImgRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
				</cfif>
			</cfcase>
			<cfcase value="SWOD">
				<cfif val(arguments.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
				<cfelseif val(arguments.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.platformFtdThumbImgRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
				</cfif>
			</cfcase>
			<cfcase value="SWB">
				<cfif val(arguments.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##arguments.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#arguments.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
				<cfelseif val(arguments.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
					<cfset local.strResult.defaultFeaturedImagePath = "#local.platformFtdThumbImgRootPath##arguments.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#arguments.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#arguments.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
				</cfif>
			</cfcase>
		</cfswitch>

		<cfif len(local.strResult.programLevelFeaturedImagePath)>
			<cfset local.strResult.featuredImagePath = local.strResult.programLevelFeaturedImagePath>
		<cfelseif len(local.strResult.defaultFeaturedImagePath)>
			<cfset local.strResult.featuredImagePath = local.strResult.defaultFeaturedImagePath>
		</cfif>

		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="trimByWordsCount" access="public" output="false" returntype="string">
		<cfargument name="inputString" type="string" required="true">
		<cfargument name="count" type="numeric" required="true">

		<cfset local.resultText = "">

		<cfset local.sanitizeOptions = {
			"allowedTags": ['p', 'a', 'ul', 'ol','li', 'b', 'i', 'strong', 'br', 'div', 'span'],
			"allowedAttributes": {}
		}>
		<cfset local.sanitizeResponse = application.objCommon.sanitizeHTML(dirtyHTML=trim(arguments.inputString), sanitizeOptions=local.sanitizeOptions)>
		<cfif local.sanitizeResponse.success>
			<cfset local.cleanInputString = trim(REREPLACE(REREPLACE(local.sanitizeResponse.content,"\t","","ALL"),"(\r\n)+","","ALL"))>
		<cfelse>
			<cfset local.cleanInputString = arguments.inputString>
		</cfif>

		<cfif len(local.cleanInputString)>
			<cfset local.resultTextArr = reMatch("([^\s]+\s?){1,#arguments.count#}",local.cleanInputString) />
			<cfif arrayLen(local.resultTextArr)>
				<cfset local.resultText = local.resultTextArr[1]>
				<cfif arrayLen(local.resultTextArr) gt 1>
					<cfset local.resultText &= "...">
				</cfif>
				<cfset local.sanitizeResponse = application.objCommon.sanitizeHTML(dirtyHTML=trim(local.resultText), sanitizeOptions=local.sanitizeOptions)>
				<cfif local.sanitizeResponse.success>
					<cfset local.resultText = local.sanitizeResponse.content>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.resultText>
	</cffunction>

	<cffunction name="addSWBillingLog" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="logType" type="string" required="true">
		<cfargument name="minutes" type="numeric" required="false" default="0">
		<cfargument name="formID" type="numeric" required="false" default="0">
		<cfargument name="billAmount" type="numeric" required="false" default="0">
		<cfargument name="billDesc" type="string" required="false" default="">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="sw_addBillingLog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.logType#">
				<cfif arguments.minutes>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.minutes#">
				<cfelse>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif arguments.formID>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">
				<cfelse>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.billAmount#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billDesc#">
				<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#DateFormat(now(),'m/d/yyyy')#">
			</cfstoredproc>

			<cfset local.strReturn = { "success": true }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn = { "success": false }>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="removeSWBillingLog" access="public" output="false" returntype="struct">
		<cfargument name="logID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryDeleteSWBillingLog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @logID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.logID#">, 
				@nowDate datetime = GETDATE();

			IF EXISTS (SELECT 1 FROM dbo.tblSWBillingLogs WHERE logID = @logID AND MONTH(dateCreated) = MONTH(@nowDate) AND YEAR(dateCreated) = YEAR(@nowDate))
				DELETE FROM dbo.tblSWBillingLogs
				WHERE logID = @logID;
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMCBillingDetails" access="public" returntype="query" output="no">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset var argOrgCode = arguments.orgcode>

		<cfstoredproc procedure="tla_getAssociations" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" null="true">
			<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#dateformat(now(),'m/d/yyyy')#">
			<cfprocresult name="local.qdepotla">
		</cfstoredproc>
		<cfset local.qrySWBilling = local.qdepotla.filter(function(row) { return row.orgcode eq argOrgCode })>

		<cfreturn local.qrySWBilling>
	</cffunction>

	<cffunction name="getEnrollmentFees" access="public" output="false" returntype="query">
		<cfargument name="referenceID" type="numeric" required="true" hint="orderID for bundle, enrollmentID for other program types">
		<cfargument name="programType" type="string" required="true">

		<cfset var qryEnrollmentFees = "">

		<cfquery name="qryEnrollmentFees" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			<cfif listFindNoCase("SWL,SWOD,SWTL", arguments.programType)>
				select sum(dt.amountBilled) as total, sum(dt.salesTaxAmount) as tax, max(dt.datePurchased) as datePurchased
				from dbo.tblEnrollments as e
				inner join dbo.tblUsers as u on u.userID = e.userID 
				inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataID = u.depoMemberDataID
				inner join trialsmith.dbo.depoTransactionsApplications as dta on dta.itemID = e.enrollmentID
					and dta.itemType = 'SWE'
				inner join trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
				where e.enrollmentID = <cfqueryparam value="#arguments.referenceID#" cfsqltype="CF_SQL_INTEGER">;
			<cfelseif arguments.programType eq "SWB">
				select sum(dt.amountBilled) as total, sum(dt.salesTaxAmount) as tax, max(dt.datePurchased) as datePurchased
				from trialsmith.dbo.depoTransactions as dt
				inner join trialsmith.dbo.depoTransactionsApplications as dta on dta.transactionID = dt.TransactionID
					and dta.itemType = 'SWBO'
					and dta.itemID = <cfqueryparam value="#arguments.referenceID#" cfsqltype="CF_SQL_INTEGER">;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEnrollmentFees>
	</cffunction>

	<cffunction name="changeRegistrantPrice" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="referenceID" type="numeric" required="true" hint="orderID for bundle, enrollmentID for other program types">
		<cfargument name="newPrice" type="numeric" required="true">
		<cfargument name="sendChangePriceEmail" type="boolean" required="true">
		<cfargument name="paymentMethod" type="string" required="true">
		<cfargument name="paymentDescription" type="string" required="true">
		<cfargument name="tspaymentMethod" type="string" required="true">
		<cfargument name="SWType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "errmsg":"" }>

		<cfset local.objBuyNow = CreateObject("component","model.buyNow.BuyNow")>

		<!--- check rights --->
		<cfset local.SemWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SemWebSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
		<cfset local.offerOnlinePaymentMethod = application.objUser.isSuperUser(cfcuser=session.cfcuser)>

		<cfset local.qryProgram = QueryNew("programID,programName,programType","integer,varchar,varchar")>
		<cfset local.hasChangeEnrollmentFeeRights = false>

		<cfif arguments.SWType eq 'SWL'>
			<cfset local.qryEnrollment = CreateObject("component","seminarWebSWL").getEnrollmentByEnrollmentID(enrollmentID=arguments.referenceID)>
			<cfset local.hasdeleteSWLRegistrantRights = (local.tmpRights.deleteSWLRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.mcproxy_siteCode) OR local.tmpRights.deleteSWLRegistrantAll is 1>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasdeleteSWLRegistrantRights AND NOT local.qryEnrollment.handlesOwnPayment AND NOT val(local.qryEnrollment.bundleOrderID)>

			<cfif QueryAddRow(local.qryProgram)>
				<cfset QuerySetCell(local.qryProgram,"programID",local.qryEnrollment.seminarID)>
				<cfset QuerySetCell(local.qryProgram,"programName",local.qryEnrollment.seminarName)>
				<cfset QuerySetCell(local.qryProgram,"programType",'SWL')>
			</cfif>
			<cfset local.acctCode = "7001">
			<cfset local.depoTransItemType = "SWE">
		<cfelseif arguments.SWType eq 'SWOD'>
			<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(enrollmentID=arguments.referenceID)>

			<cfset local.hasdeleteSWODRegistrantRights = (local.tmpRights.deleteSWODRegistrantSignUp is 1 AND local.qryEnrollment.signUpOrgCode EQ arguments.mcproxy_siteCode) OR local.tmpRights.deleteSWODRegistrantAll is 1>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasdeleteSWODRegistrantRights AND NOT local.qryEnrollment.handlesOwnPayment AND NOT val(local.qryEnrollment.bundleOrderID)>

			<cfif QueryAddRow(local.qryProgram)>
				<cfset QuerySetCell(local.qryProgram,"programID",local.qryEnrollment.seminarID)>
				<cfset QuerySetCell(local.qryProgram,"programName",local.qryEnrollment.seminarName)>
				<cfset QuerySetCell(local.qryProgram,"programType",'SWOD')>
			</cfif>
			<cfset local.acctCode = "7000">
			<cfset local.depoTransItemType = "SWE">
		<cfelseif arguments.SWType eq 'SWB'>
			<cfset local.qryBundleOrder = CreateObject("component","model.admin.seminarweb.seminarWebSWB").getBundleOrder(orderID=arguments.referenceID)>

			<cfset local.hasDeleteSWBRegistrantRights = local.qryBundleOrder.signUpOrgCode EQ arguments.mcproxy_siteCode OR application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.hasChangeEnrollmentFeeRights = local.hasDeleteSWBRegistrantRights AND NOT local.qryBundleOrder.handlesOwnPayment>

			<cfif QueryAddRow(local.qryProgram)>
				<cfset QuerySetCell(local.qryProgram,"programID",local.qryBundleOrder.bundleID)>
				<cfset QuerySetCell(local.qryProgram,"programName",local.qryBundleOrder.bundleName)>
				<cfset QuerySetCell(local.qryProgram,"programType",'SWB')>
			</cfif>
			
			<cfif local.qryBundleOrder.isSWOD is 1>
				<cfset local.acctCode = "7000">
			<cfelse>
				<cfset local.acctCode = "7001">
			</cfif>
			<cfset local.depoTransItemType = "SWBO">
		</cfif>

		<cfif NOT local.hasChangeEnrollmentFeeRights>
			<cfset local.returnStruct = { "success":false, "errmsg":"You do not have rights to this section." }>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif listFindNoCase("SWL,SWOD,SWTL", arguments.SWType)>
			<cfset local.strEnrollment = { 
				referenceID: local.qryEnrollment.enrollmentID,
				depoMemberDataID: local.qryEnrollment.depomemberdataID,
				signUpOrgCode: local.qryEnrollment.signUpOrgCode,
				email: local.qryEnrollment.email,
				firstName: local.qryEnrollment.firstName,
				lastName: local.qryEnrollment.lastName
			}>
		<cfelseif arguments.SWType eq "SWB">
			<cfset local.strEnrollment = { 
				referenceID: local.qryBundleOrder.orderID,
				depoMemberDataID: local.qryBundleOrder.depomemberdataID,
				signUpOrgCode: local.qryBundleOrder.signUpOrgCode,
				email: local.qryBundleOrder.email,
				firstName: local.qryBundleOrder.firstName,
				lastName: local.qryBundleOrder.lastName
			}>
		</cfif>

		<cfset local.qryEnrollmentFees = getEnrollmentFees(referenceID=arguments.referenceID, programType=arguments.SWType)>

		<cfquery name="local.qryGetDepoInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT dt.stateForTax AS billingState, dt.zipForTax AS billingZip, 
				ISNULL(d.firstname + ' ','') + ISNULL(d.middlename + ' ','') + ISNULL(d.lastname,'') AS registrantName
			FROM dbo.depoTransactionsApplications AS dta
			INNER JOIN dbo.depoTransactions AS dt ON dt.TransactionID = dta.transactionID
			INNER JOIN dbo.depomemberdata AS d ON d.depomemberdataID = dt.depoMemberDataID
			WHERE dta.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strEnrollment.referenceID#">
			AND dta.itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.depoTransItemType#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.currentBilledAmountIncTax = val(local.qryEnrollmentFees.total) + val(local.qryEnrollmentFees.tax)>
		<cfset local.newPrice = arguments.newPrice>
		<cfset local.actualSalestax = CreateObject("component","SWReg").getTSSalesTaxAmount(orgCode=arguments.mcproxy_siteCode, amountBilled=local.newPrice, billingState=local.qryGetDepoInfo.billingState, billingZip=local.qryGetDepoInfo.billingZip, acctCode=local.acctCode).salestax>
		<cfset local.newRegPriceIncTax = local.newPrice + local.actualSalestax>
		
		<cfset local.amountToCharge = 0>
		<cfset local.amountToRefund = 0>
		<cfif local.newRegPriceIncTax GT local.currentBilledAmountIncTax>
			<cfset local.amountToCharge = local.newRegPriceIncTax - local.currentBilledAmountIncTax>
		<cfelse>
			<cfset local.amountToRefund = local.currentBilledAmountIncTax - local.newRegPriceIncTax>
		</cfif>

		<cfset local.strResponse = { success=false, response='Invalid payment method' }>
		
		<cfif local.amountToCharge GT 0>
			<cfset local.insertPaymentTrans = false>
			<cfif arguments.paymentMethod EQ 'offline' AND local.offerOnlinePaymentMethod>
				<cfset local.strResponse.success = true>
				<cfset local.strResponse.response = "">
				<cfset local.insertPaymentTrans = true>
			<cfelseif arguments.paymentMethod EQ 'cc'>
				<cfset local.chargeDesc = "SeminarWeb.com Purchase #local.qryProgram.programName#">
				<cfset local.customerIDToUse = "olddid_#local.strEnrollment.depoMemberDataID#">
				<cfset local.paymentResponse = local.objBuyNow.chargeCC_TS(
					customerid=local.customerIDToUse,
					amount=local.amountToCharge,
					chargeDesc=local.chargeDesc,
					merchantProfile='SW',
					TransactionDepoMemberDataID=local.strEnrollment.depoMemberDataID)>

				<!--- if payment not successful --->
				<cfif NOT local.paymentResponse.ccsuccess>
					<cfset local.strResponse.response = local.paymentResponse.ccresponse>
					<cfset local.returnStruct = { "success":false, "errmsg":local.paymentResponse.ccresponse }>
					<cfreturn local.returnStruct>
				<cfelse>
					<cfset local.strResponse.success = true>
					<cfset local.strResponse.response = "">
					<cfset local.insertPaymentTrans = isDefined("local.paymentResponse.strInsertTrans")>
				</cfif>
			</cfif>
		<cfelse>
			<cfset local.strResponse.success = true>
			<cfset local.strResponse.response = "">
		</cfif>

		<cfif local.strResponse.success>
			<cfquery name="local.qryUpdateRegPrice" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @catalogOrgcode varchar(10), @siteID int, @orgID int, @programID int, @referenceID int, @depomemberdataID int, 
						@programType varchar(5), @billingstate varchar(5), @billingzip varchar(25), @linksource varchar(50), @linkterms varchar(100), 
						@price decimal(14,2), @salestax decimal(14,2), @transactionID int, @reverseEnrollmentDepoTIDList varchar(max), 
						@recordedByMemberID int, @enteredByDepoMemberDataID int, @msgjson varchar(MAX), @nowDate datetime = GETDATE(),
						@trApplicationItemType varchar(5), @statsSessionID int;

					SET @catalogOrgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProgram.programID#">;
					SET @programType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.SWType#">;
					SET @depomemberdataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strEnrollment.depoMemberDataID#">;
					SET @referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strEnrollment.referenceID#">;
					SET @billingstate = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetDepoInfo.billingstate#">;
					SET @billingzip = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetDepoInfo.billingzip#">;
					SET @linksource = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linksource#">;
					SET @linkterms = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.linkterms#">;
					SET @price = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.newPrice#">;
					SET @salestax = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.actualSalestax#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">;

					SELECT @orgID = membercentral.dbo.fn_getOrgIDFromSiteID(@siteID);

					IF @programType = 'SWB'
						SET @trApplicationItemType = 'SWBO';
					ELSE
						SET @trApplicationItemType = 'SWE';

					EXEC memberCentral.dbo.ams_getTLASITESDepoMemberDataIDByMemberID @memberID=@recordedByMemberID, @siteID=@siteID, 
						@depomemberdataid=@enteredByDepoMemberDataID OUTPUT;

					SELECT @reverseEnrollmentDepoTIDList = COALESCE(@reverseEnrollmentDepoTIDList + ',', '') + cast(dt.TransactionID as varchar(10))
					FROM dbo.depoTransactionsApplications as dta 
					INNER JOIN dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
					WHERE dta.itemID = @referenceID
					AND dta.itemType = @trApplicationItemType
					AND dt.Reversable = 'Y';

					BEGIN TRAN;
						-- reverse existing transactions
						IF @reverseEnrollmentDepoTIDList IS NOT NULL
							EXEC dbo.transactions_reverse @tidList=@reverseEnrollmentDepoTIDList, @enteredByDepoMemberDataID=@enteredByDepoMemberDataID;

						<!--- record payment transaction --->
						<cfif local.amountToCharge GT 0 AND local.insertPaymentTrans>
							<cfif arguments.paymentMethod EQ 'cc' AND isDefined("local.paymentResponse.strInsertTrans")>	
								INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid,
									sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
									statsSessionID, merchantOrgCode, refundableAmount)
								select <cfqueryparam value="#local.paymentResponse.strInsertTrans.description#" cfsqltype="CF_SQL_VARCHAR">, 
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled#" cfsqltype="CF_SQL_DOUBLE">,
										0,
									getdate(), depomemberdataid, tlamemberstate, 
									<cfqueryparam value="#local.paymentResponse.strInsertTrans.approvalCode#" cfsqltype="CF_SQL_VARCHAR">,
									<cfqueryparam value="#local.paymentResponse.strInsertTrans.paymentmethod#" cfsqltype="CF_SQL_VARCHAR">,
									<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccTransactionID#" cfsqltype="CF_SQL_VARCHAR">,
									<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseCode#" cfsqltype="CF_SQL_INTEGER">,
									<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 
									1, @statsSessionID, 'SW',
									<cfif local.paymentResponse.strInsertTrans.AmountBilled neq 0>
										<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled * -1#" cfsqltype="CF_SQL_DOUBLE">
									<cfelse>
										null
									</cfif>
								from dbo.depomemberdata
								where depomemberdataid = @depomemberdataID;

								INSERT INTO dbo.CustomerNotes (depomemberdataid, NoteTypeID, Note)
								VALUES(@depomemberdataID, 1,
									<cfqueryparam value="#abs(local.paymentResponse.strInsertTrans.AmountBilled)# SW Payment made using Authorize.net Customer Profile CustomerID #local.paymentResponse.strInsertTrans.customerid#" cfsqltype="CF_SQL_VARCHAR">);
							<cfelseif arguments.paymentMethod EQ 'offline'>
								INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid,
									sourcestate, paymentmethod, isPayment, enteredByDepomemberdataid, statsSessionID)
								select <cfqueryparam value="#arguments.paymentDescription#" cfsqltype="CF_SQL_VARCHAR">, 
										<cfqueryparam value="#local.amountToCharge * -1#" cfsqltype="CF_SQL_DOUBLE">,
										0, GETDATE(), depomemberdataid, tlamemberstate, 
										<cfqueryparam value="#arguments.tspaymentMethod#" cfsqltype="CF_SQL_VARCHAR">, 1,
										<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.depomemberdataid#">,
										@statsSessionID
								from dbo.depomemberdata
								where depomemberdataid = @depomemberdataid;
							</cfif>
						</cfif>

						<cfswitch expression="#arguments.SWType#">
							<cfcase value="SWL">
								EXEC seminarWeb.dbo.swl_buySeminar @seminarID=@programID, @depomemberdataid=@depomemberdataid,
									@billingstate=@billingstate, @billingzip=@billingzip, @linksource=@linksource, @linkterms=@linkterms,
									@orgcode=@catalogOrgcode, @price=@price, @salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;
							</cfcase>
							<cfcase value="SWOD">
								EXEC seminarWeb.dbo.swod_buySeminar @seminarID=@programID, @depomemberdataid=@depomemberdataid,
									@billingstate=@billingstate, @billingzip=@billingzip, @linksource=@linksource, @linkterms=@linkterms,
									@orgcode=@catalogOrgcode, @price=@price, @salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;
							</cfcase>
							<cfcase value="SWB">
								EXEC seminarWeb.dbo.swb_buyBundle @bundleID=@programID, @depomemberdataid=@depomemberdataid, @billingstate=@billingstate,
									@billingzip=@billingzip, @linksource=@linksource, @linkterms=@linkterms, @orgcode=@catalogOrgcode, @price=@price,
									@salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;
							</cfcase>
						</cfswitch>

						IF @transactionID > 0
							INSERT INTO dbo.depoTransactionsApplications (transactionID, itemType, itemID)
							VALUES (@transactionID, @trApplicationItemType, @referenceID);

						IF @programType = 'SWB'
							EXEC seminarWeb.dbo.sw_calculateBundleOrderRevenueAndRegFees @bundleOrderID=@referenceID, @calcOnly=0, @asOfDate=@nowDate;
						ELSE
							EXEC seminarWeb.dbo.sw_calculateEnrollmentRevenueAndRegFees @enrollmentID=@referenceID, @calcOnly=0, @asOfDate=@nowDate;
					COMMIT TRAN;

					SET @msgjson = 'Enrollment fee updated for registrant #local.qryGetDepoInfo.registrantName# on #arguments.SWType#-' + CAST(@programID AS varchar(10)) + ' from #dollarFormat(local.currentBilledAmountIncTax)# to #dollarFormat(local.newRegPriceIncTax)#.';

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS varchar(10)) + ',
						"SITEID":' + CAST(@siteID AS varchar(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
						"ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
						"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<!--- has refund --->
			<cfif local.amountToRefund GT 0>
				<cfsavecontent variable="local.refundEmailContent">
					<cfoutput>
						Registrant price has been changed and a refund may be applicable.<br/><br/>
						<b>Registration Details</b><br/><hr>
						Registrant Name: #local.qryGetDepoInfo.registrantName#<br/><br/>
						Program: #local.qryProgram.programName# (#arguments.SWType#-#local.qryProgram.programID#)<br/><br/>
						DepoMemberDataID: <a href="https://admin.trialsmith.com/TransactionView.cfm?depoMemberDataID=#local.strEnrollment.depoMemberDataID#">#local.strEnrollment.depoMemberDataID#</a><br/><br/>
						Refund Amount: #dollarformat(local.amountToRefund)#
					</cfoutput>
				</cfsavecontent>

				<cfset local.SWSiteInfo = application.objSiteInfo.getSiteInfo('SW')>
				<cfset local.emailtitle = "Refund Request for #local.qryGetDepoInfo.registrantName# on #arguments.SWType#-#local.qryProgram.programID#">

				<cfset application.objEmailWrapper.sendMailESQ(
					emailfrom={ name='SeminarWeb', email='<EMAIL>' },
					emailto=[{ name="", email="<EMAIL>" }],
					emailreplyto="",
					emailsubject=local.emailtitle,
					emailtitle=local.emailtitle,
					emailhtmlcontent=local.refundEmailContent,
					siteID=local.SWSiteInfo.siteID,
					memberID=local.SWSiteInfo.sysMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREFUNDREQ"),
					sendingSiteResourceID=local.SWSiteInfo.siteSiteResourceID
				)>
			</cfif>
			
			<!--- Check if the email should be sent --->
			<cfif arguments.sendChangePriceEmail>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.strEnrollment.signUpOrgCode)>
				<cfset local.memberID = CreateObject("component","model.seminarWeb.SWCommon").getMemberIDByDepoMemberDataID(siteCode=local.strEnrollment.signUpOrgCode, depoMemberDataID=local.strEnrollment.depomemberdataID)>
				<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=local.mc_siteInfo.siteID)>
				<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.strEnrollment.signUpOrgCode).qryAssociation>

				<cfset local.arrEmailTo = []>
				<cfif len(local.strEnrollment.email)>
					<cfset local.arrEmailTo.append({ name="#local.strEnrollment.firstName# #local.strEnrollment.lastName#", email=local.strEnrollment.email })>
				</cfif>
				<cfif listFindNoCase("SWL,SWOD,SWTL", arguments.SWType)>
					<cfif len(local.qryEnrollment.overrideEmail) AND local.strEnrollment.email NEQ local.qryEnrollment.overrideEmail>
						<cfset local.arrEmailTo.append({ name="#local.strEnrollment.firstName# #local.strEnrollment.lastName#", email=local.qryEnrollment.overrideEmail })>
					</cfif>
				</cfif>

				<cfset local.emailTitle = "Registration Price Change: #encodeForHTML(local.qryProgram.programName)#">
				<cfset local.emailBody = "">

				<cfsavecontent variable="local.emailBody">
					<cfoutput>
					<cfif local.amountToRefund GT 0>
						#local.strEnrollment.firstName# #local.strEnrollment.lastName#:<br/><br/>
						This email confirms that we've adjusted the price for your registration: #local.qryProgram.programName#. 
						The price was changed from #dollarformat(local.currentBilledAmountIncTax)# to #dollarformat(local.newRegPriceIncTax)#. 
						A refund of #dollarformat(local.amountToRefund)# will be sent to our accounting department. 
						Please allow 3 business days for the changed price to be processed.<br/><br/>
						If you have any questions about this price change, please contact us at #local.mc_siteInfo.supportProviderEmail#.<br/><br/>
						#local.qryAssociation.emailFrom#<br/>
						#local.qryAssociation.supportPhone#
					<cfelse>
						#local.strEnrollment.firstName# #local.strEnrollment.lastName#:<br/><br/>
						This email confirms that we've adjusted the price for your registration: #local.qryProgram.programName#. 
						The price was changed from #dollarformat(local.currentBilledAmountIncTax)# to #dollarformat(local.newRegPriceIncTax)#. 
						An additional charge of #dollarformat(local.amountToCharge)# will be sent to our accounting department. 
						Please allow 3 business days for the changed price to be processed.<br/><br/>
						If you have any questions about this price change, please contact us at #local.mc_siteInfo.supportProviderEmail#.<br/><br/>
						#local.qryAssociation.emailFrom#<br/>
						#local.qryAssociation.supportPhone#
					</cfif>
					</cfoutput>
				</cfsavecontent>

				<cfset application.objEmailWrapper.sendMailESQ(
					emailfrom={ name=local.qryAssociation.emailFrom, email=local.mc_siteInfo.networkEmailFrom },
					emailto=local.arrEmailTo,
					emailreplyto=local.mc_siteInfo.supportProviderEmail,
					emailsubject=local.emailTitle,
					emailtitle=local.emailTitle,
					emailhtmlcontent=local.emailBody,
					siteID=local.mc_siteInfo.siteID,
					memberID=local.memberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBPRCCNGREQ"),
					sendingSiteResourceID=local.SeminarWebAdminSRID
				)>
			</cfif>

			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errmsg = local.strResponse.response>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSWMerchantProfile" access="public" output="false" returntype="query">
		<cfset var qrySWProfile = "">

		<cfquery name="qrySWProfile" datasource="#application.dsn.memberCentral.dsn#">
			SELECT top 1 mp.profileID, mp.profileCode, g.gatewayClass, mp.tabTitle
			FROM dbo.mp_profiles as mp
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			WHERE mp.siteID = dbo.fn_getSiteIDFromSiteCode('TS')
			AND mp.profileCode = 'AuthorizeCCSemWeb'
			AND mp.status = 'A'
			AND g.isActive = 1
		</cfquery>

		<cfreturn qrySWProfile>
	</cffunction>

	<cffunction name="getParticipantIDFromSiteID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryParticipant = "">

		<cfquery name="qryParticipant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.participantID
			FROM dbo.tblParticipants AS p
			INNER JOIN membercentral.dbo.sites AS s ON s.siteCode = p.orgCode
			WHERE s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND p.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryParticipant.participantID)>
	</cffunction>

	<cffunction name="getParticipantIDFromSiteCode" access="public" output="false" returntype="numeric">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var qryParticipant = "">

		<cfquery name="qryParticipant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT p.participantID
			FROM dbo.tblParticipants AS p
			INNER JOIN membercentral.dbo.sites AS s ON s.siteCode = p.orgCode
			WHERE s.siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">
			AND p.isActive = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryParticipant.participantID)>
	</cffunction>

	<cffunction name="saveSWProgramRateSyndication" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="xmlSynd" type="string" required="yes">
		<cfargument name="allowSyndication" type="boolean" required="yes">
		<cfargument name="pushDefaultPricingToOptIns" type="boolean" required="yes">
		<cfargument name="allowOptInRateChange" type="boolean" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.participantID = getParticipantIDFromSiteCode(sitecode=arguments.mcproxy_siteCode)>

			<cfstoredproc procedure="sw_saveProgramRateSyndication" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowSyndication#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.pushDefaultPricingToOptIns#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowOptInRateChange#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.xmlSynd#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
			<cfif FindNoCase("invalid request",cfcatch.message)>
				<cfset local.returnStruct.err = "You do not have rights to this section.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="isActiveOptedIntoProgram" access="public" output="false" returntype="boolean">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var qryProgram = "">

		<cfquery name="qryProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @programID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				@participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">

			<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
				SELECT seminarID AS programID
				FROM dbo.tblSeminarsOptIn
				WHERE seminarID = @programID
				AND participantID = @participantID
				AND isActive = 1;
			<cfelseif arguments.programType eq "SWB">
				SELECT bundleID AS programID
				FROM dbo.tblBundlesOptIn
				WHERE bundleID = @programID
				AND participantID = @participantID
				AND isActive = 1;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryProgram.recordCount IS 1>
	</cffunction>

	<cffunction name="hasSWProgramRateChangeRights" access="public" output="false" returntype="boolean">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var qryProgram = queryNew('')>
		
		<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
			<cfquery name="qryProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT p.orgCode, s.allowOptInRateChange
				FROM dbo.tblSeminars AS s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelseif arguments.programType eq "SWB">
			<cfquery name="qryProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT p.orgCode, b.allowOptInRateChange
				FROM dbo.tblBundles AS b
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = b.participantID
				WHERE b.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfreturn qryProgram.recordCount AND (qryProgram.orgCode EQ arguments.siteCode OR qryProgram.allowOptInRateChange IS 1)>
	</cffunction>

	<cffunction name="getSWProgramRateSettings" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">

		<cfset var qryProgramRateSettings = "">

		<cfquery name="qryProgramRateSettings" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT isPriceBasedOnActual, freeRateDisplay
			FROM dbo.fn_getSWProgramRateSettings (
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryProgramRateSettings>
	</cffunction>

	<cffunction name="saveSWSyndProgramRateOptions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasSemWebRights(siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.programID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.participantID = getParticipantIDFromSiteCode(sitecode=arguments.mcproxy_siteCode)>

			<cfstoredproc procedure="sw_updateSyndicatedProgramPricing" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
			<cfif FindNoCase("invalid request",cfcatch.message)>
				<cfset local.returnStruct.err = "You do not have rights to this section.">
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getUploadFileSettingsForSubmitProgram" access="public" output="false" returntype="struct">
		<cfargument name="orgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryParticipant" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT participantID, orgCode AS participantOrgCode
			FROM seminarWeb.dbo.tblParticipants
			WHERE orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgCode#">;
		</cfquery>

		<cfset local.bucket = "seminarweb">
		<cfset local.returnStruct["accesskey"] = "********************">
		<cfset local.secretKey = "UJZ/JsekvjTNCJOK3UmUUPIAJwEwsoiqnq2jybdX">

		<cfset local.http = "http://">
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.http = "https://">
		</cfif>

		<cfset local.returnStruct["uploadurl"] = "#local.http##local.bucket#.s3.amazonaws.com">
		<cfset local.returnStruct["objectkey"] = lcase("swodsubmissions/#application.MCEnvironment#/#local.qryParticipant.participantOrgCode#/#local.qryParticipant.participantID#/")>

		<!--- generating policy & signature here due to the difference in aws access & secret key @application.objS3 --->
		<cfset local.dateString = DateAdd("d",1,Now())>

		<cfsavecontent variable="local.stringPolicy">
			<cfoutput>
			{
				"expiration": "#DateFormat(local.dateString,"yyyy-mm-dd")#T12:00:00.000Z",
				"conditions": [
				{"bucket": "#local.bucket#" },
				{"acl": "public-read" },
				["starts-with", "$key", "#left(local.returnStruct.objectkey,len(local.returnStruct.objectkey)-1)#"],
				["starts-with", "$content-type", ""],
				["starts-with", "$name", ""],
				["starts-with", "$filename", ""]
				]
			}
			</cfoutput>
		</cfsavecontent>
		
		<!--- Replace "\n" with chr(10) to get a correct digest --->
		<cfset local.fixedData = replace(local.stringPolicy,"\n",chr(10),"all")>
		<cfset local.returnStruct["policy"] = ToBase64(local.fixedData)>

		<!--- Calculate the hash of the information --->
		<cfset local.signingKey = createObject("java","javax.crypto.spec.SecretKeySpec").init(local.secretKey.getBytes(),'HmacSHA1')>
		<cfset local.mac = createObject("java","javax.crypto.Mac").getInstance('HmacSHA1')>
		<cfset local.mac.init(local.signingKey)>
		<cfset local.returnStruct["policysignature"] = toBase64(mac.doFinal(local.returnStruct.policy.getBytes()))>

		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="checkProgramCode" access="public" output=false returntype="struct">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qryCheck" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT dbo.fn_sw_isUniqueProgramCode(
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.programCode#">
			) AS isUniqueCode;
		</cfquery>

		<cfset local.data.success = local.qryCheck.isUniqueCode eq 1>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSWAppSettingsXML" access="public" output="false" returntype="xml">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySWAppSettings = "">

		<cfquery name="qrySWAppSettings" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @applicationTypeID int = dbo.fn_getApplicationTypeIDFromName('SemWebCatalog'),
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@settingsXML xml;

			select @settingsXML = settingsXML
			from dbo.cms_applicationTypeSettings
			where siteID = @siteID
			and applicationTypeID = @applicationTypeID;

			select isnull(@settingsXML,'<settings />') as settingsXML;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySWAppSettings.settingsXML>
	</cffunction>

	<cffunction name="getRegistrants" access="public" output="false" returntype="Any">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="rdateFrom" type="string" required="true">
		<cfargument name="rdateTo" type="string" required="true">
		<cfargument name="rHideDeleted" type="boolean" required="true">
		<cfargument name="pKeyword" type="string" required="true">
		<cfargument name="pProgramCode" type="string" required="true">
		<cfargument name="pHideInactive" type="boolean" required="true">
		<cfargument name="pPublisher" type="string" required="true">
		<cfargument name="pformat" type="string" required="true">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">
		
		<cfset var local = StructNew()>

		<cfset local.arrCols = arrayNew(1)>		
		<cfset arrayAppend(local.arrCols,"tmp.dateEnrolled")>
		<cfset arrayAppend(local.arrCols,"m2.LastName + ', ' + m2.FirstName")>
		<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @totalCount int;

				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;
				CREATE TABLE ##tmpEnrollments (enrollmentID int PRIMARY KEY, MCMemberID int, signUpOrgcode varchar(10), dateEnrolled smalldatetime, seminarID int, 
					seminarName varchar(250), programFormat varchar(4), isNATLE bit, publisherOrgcode varchar(10), isActive bit);
				CREATE TABLE ##tmpEnrollments2 (enrollmentID int PRIMARY KEY, MCMemberID int, firstName varchar(75), lastName varchar(75), memberNumber varchar(50), 
					company varchar(200), seminarID int, seminarName varchar(250), dateEnrolled datetime, signUpOrgCode varchar(10), programFormat varchar(4),
					isNATLE bit, publisherOrgcode varchar(10), isActive bit, row int);

				<cfif arguments.rDateFrom NEQ ''>
					DECLARE @rDateFrom smalldatetime = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.rDateFrom#">;	
				</cfif>
				<cfif arguments.rDateTo NEQ ''>
					DECLARE @rDateTo smalldatetime = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rDateTo# 23:59">; <!--- smalldatetime - no seconds --->
				</cfif>
				<cfif arguments.pPublisher NEQ ''>
					DECLARE @pPublisher int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pPublisher#">;
				</cfif>

				INSERT INTO ##tmpEnrollments (enrollmentID, MCMemberID, signUpOrgcode, dateEnrolled, seminarID, seminarName, programFormat, isNatle, publisherOrgCode, isActive)
				SELECT e.enrollmentID, e.MCMemberID, ep.orgcode, e.dateEnrolled, s.seminarID, s.seminarName, 
					<cfif listFindNoCase("SWL,SWOD",arguments.pformat)>'#arguments.pformat#'<cfelse>case when ex.seminarID is not null then 'SWL' ELSE 'SWOD' end</cfif>, 
					<cfif arguments.pformat eq "SWOD">0<cfelse>isnull(ex.isNatle,0)</cfif>, sp.orgcode, e.isActive
				FROM dbo.tblEnrollments e
				INNER JOIN dbo.tblParticipants ep ON e.participantID = ep.participantID 
				INNER JOIN dbo.tblSeminars s ON s.seminarID = e.seminarID
					AND s.isDeleted = 0
					<cfif len(arguments.pKeyword)>
						AND s.seminarName + ISNULL(' ' + s.seminarSubTitle,'') LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.pKeyword#%">
					</cfif>
					<cfif len(arguments.pProgramCode)>
						and s.programCode = <cfqueryparam value="#arguments.pProgramCode#" cfsqltype="cf_sql_varchar">
					</cfif>
					<cfif arguments.pHideInactive is 1>
						AND s.isPublished = 1
					</cfif>
					<cfif arguments.pPublisher NEQ ''>
						AND s.participantID = @pPublisher
					</cfif>
				INNER JOIN dbo.tblParticipants sp ON s.participantID = sp.participantID 
				INNER JOIN dbo.tblUsers u ON e.userID = u.userID
				INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
					AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
				<cfif arguments.pformat EQ 'SWL'>
					INNER JOIN dbo.tblSeminarsSWLive as ex ON s.seminarID = ex.seminarID
				<cfelseif arguments.pformat EQ 'SWOD'>
					INNER JOIN dbo.tblSeminarsSWOD as ex ON s.seminarID = ex.seminarID 
				<cfelse>
					LEFT OUTER JOIN dbo.tblSeminarsSWLive as ex on s.seminarID = ex.seminarID
				</cfif>	
				WHERE 1=1
				<cfif arguments.rHideDeleted eq 1>
					AND e.isActive = 1
				</cfif>
				<cfif arguments.rDateFrom NEQ '' and arguments.rDateTo NEQ ''>
					AND e.dateEnrolled BETWEEN @rDateFrom AND @rDateTo
				<cfelseif arguments.rDateFrom NEQ ''>
					AND e.dateEnrolled >= @rDateFrom
				<cfelseif arguments.rDateTo NEQ ''>
					AND e.dateEnrolled <= @rDateTo
				</cfif>;

				INSERT INTO ##tmpEnrollments2 (enrollmentID, MCMemberID, firstName, lastName, memberNumber, company, seminarID, seminarName, 
					dateEnrolled, signUpOrgCode, programFormat, isNatle, publisherOrgCode, isActive, row)
				SELECT tmp.enrollmentID, m2.memberID, m2.FirstName, m2.LastName, m2.membernumber, m2.Company, tmp.seminarID, tmp.seminarName,
					tmp.dateEnrolled, tmp.signupOrgCode, tmp.programFormat, tmp.isNatle, tmp.publisherOrgCode, tmp.isActive, 
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
				FROM ##tmpEnrollments as tmp
				INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = tmp.MCMemberID
				INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID;

				SET @totalCount = @@ROWCOUNT;

				<cfif arguments.mode eq "exportregsearch">
					DECLARE @selectsql varchar(max) = '
						SELECT FirstName, LastName, MemberNumber, Company, programformat+''-''+cast(seminarID as varchar(10)) as SeminarID, 
							SeminarName, DateEnrolled as Registered, signUpOrgCode as EnrolledOnSite, ProgramFormat, 
							case when isNATLE = 1 then ''Yes'' else ''No'' end as isNATLE, publisherOrgCode as Publisher, 
							case when IsActive = 1 then ''Yes'' else ''No'' end as IsActive, ROW_NUMBER() OVER(order by row) as mcCSVorder
						*FROM* ##tmpEnrollments2';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
				<cfelse>
					DECLARE @posStart int, @posStartAndCount int;
					SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

					SELECT enrollmentID, MCMemberID as memberID, firstName, lastName, memberNumber, company, seminarID, seminarName, 
						dateEnrolled, signUpOrgCode, programFormat, isNATLE, publisherOrgCode, isActive, row, @totalCount as totalCount
					FROM ##tmpEnrollments2
					WHERE row > @posStart AND row <= @posStartAndCount
					ORDER BY row;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif arguments.mode neq "exportregsearch">
			<cfreturn local.qryEnrollments>
		</cfif>
	</cffunction>

	<cffunction name="getSeminarWebAdminRights" access="public" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn local.tmpRights>
	</cffunction>

	<cffunction name="hasSemWebRights" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="checkLockSettings" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWInfo">
			SET NOCOUNT ON;

			DECLARE @siteID int, @siteCode varchar(10), @publisherOrgCode varchar(10) = '', @lockSettings bit,
				@handlesOwnPayment bit, @allowSyndication bit = 0, @allowOptInRateChange bit = 0;

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @siteCode = memberCentral.dbo.fn_getSiteCodeFromSiteID(@siteID);
			
			<cfif listFindNoCase("SWL,SWOD",arguments.programType)>
				SELECT @publisherOrgCode = p.orgcode, @lockSettings = s.lockSettings, @handlesOwnPayment = p.handlesOwnPayment,
					@allowOptInRateChange = s.allowOptInRateChange,
					<cfif arguments.programType eq "SWL">
						@allowSyndication = swl.allowSyndication
					<cfelse>
						@allowSyndication = swod.allowSyndication
					</cfif>
				FROM dbo.tblSeminars AS s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
				WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				AND s.isDeleted = 0;
			<cfelseif arguments.programType eq "SWB">
				SELECT @publisherOrgCode = p.orgcode, @lockSettings = b.lockSettings, @handlesOwnPayment = p.handlesOwnPayment,
					@allowOptInRateChange = b.allowOptInRateChange
				FROM dbo.tblBundles AS b 
				INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
				WHERE b.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
			<cfelseif arguments.programType eq "SWTL">
				SELECT @publisherOrgCode = p.orgcode, @lockSettings = s.lockSettings, @handlesOwnPayment = p.handlesOwnPayment
				FROM dbo.tblTitles AS t
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = t.participantID
				INNER JOIN dbo.tblSeminarsAndTitles AS sat ON sat.titleID = t.titleID
				INNER JOIN dbo.tblSeminars AS s ON sat.seminarID = s.seminarID
				WHERE t.titleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				AND t.isDeleted = 0;
			</cfif>

			SELECT isPublisher = CASE WHEN @publisherOrgCode = @siteCode THEN 1 ELSE 0 END,
				@lockSettings as lockSettings, @handlesOwnPayment as handlesOwnPayment, @allowSyndication as allowSyndication,
				@allowOptInRateChange as allowOptInRateChange;
		</cfquery>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfset local.editProgramAllPerm = "">
		<cfset local.editProgramPublishPerm = "">
		<cfif arguments.programType eq "SWL">
			<cfset local.editProgramAllPerm = "editSWLProgramAll">
			<cfset local.editProgramPublishPerm = "editSWLProgramPublish">
		<cfelseif arguments.programType eq "SWOD" OR arguments.programType eq "SWTL">
			<cfset local.editProgramAllPerm = "editSWODProgramAll">
			<cfset local.editProgramPublishPerm = "editSWODProgramPublish">
		<cfelseif arguments.programType eq "SWB">
			<cfset local.editProgramAllPerm = "editBundleAll">
			<cfset local.editProgramPublishPerm = "editBundlePublish">
		</cfif>

		<cfswitch expression="#arguments.action#">
			<cfcase value="Edit">
				<cfset local.hasRights = (local.tmpRights[local.editProgramAllPerm] is 1 OR local.tmpRights[local.editProgramPublishPerm] is 1) AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="ManageRates">
				<cfset local.hasRights = local.tmpRights[local.editProgramAllPerm] is 1 OR local.tmpRights[local.editProgramPublishPerm] is 1>
			</cfcase>
			<cfcase value="ManageRateChanges">
				<cfset local.hasRights = (local.tmpRights[local.editProgramAllPerm] is 1 OR local.tmpRights[local.editProgramPublishPerm] is 1)
					AND (local.qrySWInfo.isPublisher EQ 1 OR local.qrySWInfo.allowOptInRateChange EQ 1)>
			</cfcase>
			<cfcase value="toggleAllowRegistrants">
				<cfif arguments.programType eq "SWL">
					<cfset local.hasRights = (local.tmpRights.toggleSWLRegistrantsPublish is 1 AND local.qrySWInfo.isPublisher EQ 1) OR local.tmpRights.toggleSWLRegistrantsAll is 1>
				<cfelseif arguments.programType eq "SWOD">
					<cfset local.hasRights = (local.tmpRights.toggleSWODRegistrantsPublish is 1 AND local.qrySWInfo.isPublisher EQ 1) OR local.tmpRights.toggleSWODRegistrantsAll is 1>
				</cfif>
			</cfcase>
			<cfcase value="deleteProgram">
				<cfset local.hasRights = local.tmpRights.deleteProgram is 1 AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="manageSWOptIns">
				<cfset local.hasRights = local.tmpRights.manageSWOptIns is 1 AND val(local.qrySWInfo.handlesOwnPayment) EQ 0>
			</cfcase>
			<cfcase value="manageVideoPreview">
				<cfif arguments.programType eq "SWOD" or arguments.programType eq "SWTL">
					<cfset local.hasRights = (local.tmpRights[local.editProgramAllPerm] is 1 OR local.tmpRights[local.editProgramPublishPerm] is 1) AND local.qrySWInfo.isPublisher EQ 1>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfreturn local.hasRights AND (arguments.checkLockSettings ? NOT local.qrySWInfo.lockSettings : true)>
	</cffunction>

	<cffunction name="getSeminarSyndicateData" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var qrySeminarData = "">
		
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="qrySeminarData">
			SELECT soi.introMessageText, soi.endOfSeminarText, soi.isActive, soi.sellCatalog
			FROM dbo.tblSeminarsOptIn soi
			INNER JOIN dbo.tblParticipants tp ON tp.participantID = soi.participantID
				AND tp.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#">
			WHERE soi.seminarid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
		</cfquery>

		<cfreturn qrySeminarData>
	</cffunction>

	<cffunction name="getBundleSyndicateData" access="public" output="false" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var qryBundleData = "">
		
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="qryBundleData">
			SELECT boi.isActive, boi.sellCatalog
			FROM dbo.tblBundlesOptIn boi
			INNER JOIN dbo.tblParticipants tp ON tp.participantID = boi.participantID
				AND tp.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#">
			WHERE boi.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
		</cfquery>

		<cfreturn qryBundleData>
	</cffunction>

	<cffunction name="getEnrollmentCount" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">

		<cfset var qryEnrollmentCount = "">

		<cfquery name="qryEnrollmentCount" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @seminarID int, @participantID int;

			SET @seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
			SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			SELECT count(e.enrollmentID) as count
			FROM dbo.tblEnrollments AS e 
			INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
			INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
			WHERE e.seminarID = @seminarID
			AND e.isActive = 1
				AND (s.participantID = @participantID OR e.participantID = @participantID)
			AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
		</cfquery>
		
		<cfreturn qryEnrollmentCount>
	</cffunction>

</cfcomponent>