<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.js">
	<cfoutput>
	<style>
	.mcModalBodyCustom {
		min-height:100px;
	}
	</style>
	<script type="text/javascript" src="/assets/admin/javascript/manageSubscribers.js#local.assetCachingKey#"></script>
	
	<script language="javascript">
		var #ToScript(local.subscribersXML,"subsXML")#
		var mcg_gridQString = subsXML + '&stID=#local.SubReportFilter.listFilter.fSubType#&subID=#local.SubReportFilter.listFilter.fSubscription#&freqID=#local.SubReportFilter.listFilter.fFreq#&rateID=#local.SubReportFilter.listFilter.fRate#&dtTSf=#local.SubReportFilter.listFilter.fTermStartFrom#&dtTSt=#local.SubReportFilter.listFilter.fTermStartTo#&dtTEf=#local.SubReportFilter.listFilter.fTermEndFrom#&dtTEt=#local.SubReportFilter.listFilter.fTermEndTo#&dtOffrExpF=#local.SubReportFilter.listFilter.fOffrExpFrom#&dtOffrExpT=#local.SubReportFilter.listFilter.fOffrExpTo#&spID=#local.SubReportFilter.listFilter.fSubPaymentStatus#&sID=#local.SubReportFilter.listFilter.fSubStatus#&fCard=#local.SubReportFilter.listFilter.fHasCardOnFile#&chkAll=#local.checkAll#&associatedMemberID=#local.SubReportFilter.listFilter.associatedMemberID#&associatedGroupID=#local.SubReportFilter.listFilter.associatedGroupID#&RevGL=#local.SubReportFilter.listFilter.fRevGL#';
		var statusStart = '#local.SubReportFilter.listFilter.fSubStatus#';

		#ToScript(this.link.listReports,"link_listReports")#
		#ToScript(this.link.startExportSubscriptions,"link_exportSubs")#
		#ToScript(this.link.startMarkExpired,"link_startMarkExpired")#
		#ToScript(this.link.startGenerateSub,"link_startGenerateSub")#
		#ToScript(this.link.startMassUpdateGraceEndDate,"link_startUpdateGraceEndDate")#
		#ToScript(this.link.startMarkInactive,"link_startMarkInactive")#
		#ToScript(this.link.startMarkActive,"link_startMarkActive")#
		#ToScript(this.link.startMarkBilled,"link_startMarkBilled")#
		#ToScript(this.link.startForceAddSub,"link_startForceAddSub")#
		#ToScript(this.link.startDeleteRenewals,"link_startDeleteRenewals")#
		#ToScript(this.link.removePaymentMethod,"link_removePaymentMethod")#
		#ToScript(this.link.startRemoveAddons,"link_startRemoveAddons")#
		#ToScript(this.link.startGenerateOffers,"link_startGenerateOffers")#
		#ToScript(this.link.startMarkAccepted,"link_startMarkAccepted")#
		#ToScript(this.link.startUpdateOfferExpirationDate,"link_startUpdateOfferExpirationDate")#
		#ToScript(local.subRenewalLink,"link_subRenewal")#
		#ToScript(this.link.dspRemovePaymentMethod,"link_removePaymethod")#

		$(function(){
			loadSubscribersTab();

			<cfif local.SubReportFilter.listFilter.fSubType gt 0>
				$('##selfSubType').val(#local.SubReportFilter.listFilter.fSubType#);
				
				mca_callChainedSelect('fSubType', 'selfSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubReportFilter.listFilter.fSubscription#, false, false);
				<cfif local.SubReportFilter.listFilter.fSubscription gt 0>
					mca_callChainedSelect('fSubscription', 'selfRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubReportFilter.listFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>
			<cfif arguments.event.getValue('ig',0) is 1>
				mcg_init();
				initgrid=1;
				$('##divSubBtnBar').show();
			</cfif>
		});
	</script>
	<style type="text/css">
		div##mcg_gridbox tr td:first-child div.hdrcell { padding-left:5px; }	
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div class="row my-3">
	<div class="col">
		<form action="#this.link.listReports#" method="POST" name="frmFilter" id="frmFilter">
			<input type="hidden" name="fSubType" id="fSubType" value="#local.SubReportFilter.listFilter.fSubType#">
			<input type="hidden" name="fSubscription" id="fSubscription" value="#local.SubReportFilter.listFilter.fSubscription#">
			<input type="hidden" name="fRate" id="fRate" value="#local.SubReportFilter.listFilter.fRate#">
			<input type="hidden" name="fRevenueGL" id="fRevenueGL" value="0">
			<input type="hidden" name="fChkAll" id="fChkAll" value="#local.checkAll#">
			<input type="hidden" name="fChkedSubs" id="fChkedSubs" value="">
			<input type="hidden" name="fUnchkedSubs" id="fUnchkedSubs" value="">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Filter Subscribers
					</div>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="selfSubType" id="selfSubType" class="form-control">
										<option value="0">All Subscription Types</option>
										<cfloop query="local.qrySubTypes">
											<option value="#local.qrySubTypes.typeID#">#local.qrySubTypes.typeName#</option>
										</cfloop>
									</select>
									<label for="selfSubType">Subscription Type</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="selfSubscription" id="selfSubscription" class="form-control">
										<option value="0">All Subscriptions</option>
									</select>
									<label for="selfSubscription">Subscription</label>
								</div>
							</div>
							<div class="form-group">
								<div class="d-flex align-items-center mb-1">
									<span class="text-grey small mx-1">Quickly Select: </span>
									<a href="javascript:quickSelectSubRates('selfRate',0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
									<a href="javascript:quickSelectSubRates('selfRate',1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
								</div>
								<div class="form-label-group mb-2">
									<select name="selfRate" id="selfRate" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
									<label for="selfRate">Rate</label>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartFrom" id="fTermStartFrom" value="#local.SubReportFilter.listFilter.fTermStartFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartFrom">Start Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartTo" id="fTermStartTo" value="#local.SubReportFilter.listFilter.fTermStartTo#" size="16" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartTo">Start Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndFrom" id="fTermEndFrom" value="#local.SubReportFilter.listFilter.fTermEndFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndFrom">End Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndTo" id="fTermEndTo" value="#local.SubReportFilter.listFilter.fTermEndTo#" size="16" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndTo">End Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row offrExpWrap<cfif local.SubReportFilter.listFilter.fSubStatus neq 'O'> d-none</cfif>">
								<div class="col">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fOffrExpFrom" id="fOffrExpFrom" value="#local.SubReportFilter.listFilter.fOffrExpFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fOffrExpFrom">Offer Expiration From</label>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fOffrExpTo" id="fOffrExpTo" value="#local.SubReportFilter.listFilter.fOffrExpTo#" size="16" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fOffrExpTo">Offer Expiration To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubStatus" id="fSubStatus" class="form-control">
										<option value="0">All Statuses</option>
										<cfloop query="local.qryStatuses">
											<option value="#local.qryStatuses.statusCode#"<cfif local.SubReportFilter.listFilter.fSubStatus eq local.qryStatuses.statusCode> selected</cfif>>#local.qryStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubStatus">Status</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubPaymentStatus" id="fSubPaymentStatus" class="form-control">
										<option value="0">All Activation Options</option>
										<cfloop query="local.qryPaymentStatuses">
											<option value="#local.qryPaymentStatuses.statusCode#"<cfif local.SubReportFilter.listFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode> selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubPaymentStatus">Activation Option</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fFreq" id="fFreq" class="form-control">
										<option value="0">All Frequencies</option>
										<cfloop query="local.qryFrequencies">
											<option value="#local.qryFrequencies.frequencyID#" <cfif local.SubReportFilter.listFilter.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
										</cfloop>
									</select>
									<label for="fFreq">Frequency</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fHasCardOnFile" id="fHasCardOnFile" class="form-control">
										<option value="">With or Without Pay Method Associated</option>
										<option value="Y"<cfif local.SubReportFilter.listFilter.fHasCardOnFile eq "Y"> selected</cfif>>With Pay Method Associated</option>
										<option value="N"<cfif local.SubReportFilter.listFilter.fHasCardOnFile eq "N"> selected</cfif>>With no Pay Method Associated</option>
									</select>
									<label for="fHasCardOnFile">Pay Method</label>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group row">
						<div class="col-md-12">
							<div class="row">
								<div class="col-auto">
									Associated With:
								</div>
								<div class="col">
									<div class="form-check form-check-inline">
										<input type="radio" name="assocType" id="assocTypeMember" class="assocType form-check-input"<cfif local.SubReportFilter.listFilter.associatedMemberID gt 0> checked</cfif> value="member">
										<label class="form-check-label" for="assocTypeMember">A Specific Member</label>
									</div>
									<div class="form-check form-check-inline">
										<input type="radio" name="assocType" id="assocTypeGroup" class="assocType form-check-input"<cfif local.SubReportFilter.listFilter.associatedGroupID gt 0 >checked</cfif> value="group">
										<label class="form-check-label" for="assocTypeGroup">A Specific Group</label>
									</div>
									<div id="divAssociatedVal">
										<span id="associatedVal" class="font-weight-bold">
											<cfset local.showExpandSearch = false>
											<cfif local.SubReportFilter.listFilter.associatedMemberID gt 0>
												<cfset local.showExpandSearch = true>
												#local.SubReportFilter.listFilter.associatedMemberName# (#local.SubReportFilter.listFilter.associatedMemberNum#)
											<cfelseif local.SubReportFilter.listFilter.associatedGroupID gt 0>
												#local.SubReportFilter.listFilter.associatedGroupName#
											</cfif>
										</span>
										<a href="##" id="aClearAssocType" class="ml-2">clear</a>
									</div>
									<input type="hidden" name="associatedMemberID" id="associatedMemberID" value="#local.SubReportFilter.listFilter.associatedMemberID#">
									<input type="hidden" name="associatedMemberName" id="associatedMemberName" value="#local.SubReportFilter.listFilter.associatedMemberName#">
									<input type="hidden" name="associatedMemberNum" id="associatedMemberNum" value="#local.SubReportFilter.listFilter.associatedMemberNum#">
									<input type="hidden" name="associatedGroupID" id="associatedGroupID" value="#local.SubReportFilter.listFilter.associatedGroupID#">
									<input type="hidden" name="associatedGroupName" id="associatedGroupName" value="#local.SubReportFilter.listFilter.associatedGroupName#">
								</div>
								<div class="row col-md-12" id="expandSearch"<cfif NOT local.showExpandSearch> style="display:none"</cfif>>
									<div class="col-md-12">
										<div class="row">
											<div class="col-sm-12 pr-md-0 font-weight-bold my-2">
												Expand search to consider linked records
											</div>
											<div class="col-md-10 col-sm-12">
												<div class="form-check form-check-inline">
													<input type="radio" name="linkedRecords" id="linkedRecordsAll" value="all" class="form-check-input" <cfif local.SubReportFilter.listFilter.linkedRecords EQ "all">checked="true"</cfif>>
													<label class="form-check-label" for="linkedRecordsAll">Include children of the selected records in search</label>
												</div>
											</div>
											<div class="col-md-10 col-sm-12">
												<div class="form-check form-check-inline">
													<input type="radio" name="linkedRecords" id="linkedRecordSelected" value="selected" class="form-check-input" <cfif local.SubReportFilter.listFilter.linkedRecords EQ "selected">checked="true"</cfif>>
													<label class="form-check-label" for="linkedRecordSelected">Only include the specific selected records</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="card-footer p-2 text-right">
					<button type="button" name="btnResetFilterSubs" class="btn btn-sm btn-secondary" onclick="clearFilterSubGrid();">Clear Filters</button>
					<button type="button" name="btnFilterSubs" class="btn btn-sm btn-primary" onclick="filterSubGrid();">
						<i class="fa-light fa-filter"></i> Filter Subscribers
					</button>
				</div>
			</div>
		</form>
	</div>
</div>

<div><span id="mcg_rnum"></span><span id="selSubCountDisp" data-selcount="0"></span></div>
<div id="mcg_gridbox" class="mt-2" style="min-width:700px;height:450px;"></div>
<div id="divLegend" class="mt-2" style="display:none;">#local.objSubs.showSubscriptionStatusLegend('nonactivated,deleted')#</div>

<div id="divSubBtnBar" class="mt-2" style="display:none;">
	<cfswitch expression="#local.SubReportFilter.listFilter.fSubStatus#">
		<cfcase value="P">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkExpire" type="button" onclick="checkMarkExpired();">Expire Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkGenSub" type="button" onclick="startGenSub();">Generate Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnUpdateGraceDate" type="button" onclick="massUpdateGraceEndDate();">Mass Change Grace End Dates</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="A">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkExpire" type="button" onclick="checkMarkExpired();">Expire Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkInactive" type="button" onclick="checkMarkInactive();">Mark as Inactive</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkGenSub" type="button" onclick="startGenSub();">Generate Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnUpdateGraceDate" type="button" onclick="massUpdateGraceEndDate();">Mass Change Grace End Dates</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="I">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkExpire" type="button" onclick="checkMarkExpired();">Expire Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkInactive" type="button" onclick="checkMarkActive();">Mark as Active</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkGenSub" type="button" onclick="startGenSub();">Generate Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="E">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkGenSub" type="button" onclick="startGenSub();">Generate Subscriptions</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemPmtMthd" type="button" onclick="removePaymentMethods();">Remove Payment Method</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="R">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkBilled" type="button" onclick="checkMarkBilled();">Mark as Billed</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnAddSub" type="button" onclick="startAddSub(1);">Add Subscription</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnUpdateGraceDate" type="button" onclick="massUpdateGraceEndDate();">Mass Change Grace End Dates</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnDelOffer" type="button" onclick="deleteRenewals();">Delete Selected Renewals</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemPmtMthd" type="button" onclick="removePaymentMethods();">Remove Payment Method</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemAddons" type="button" onclick="removeAddOn();">Remove Addons</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="O">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnSendOffer" type="button" onclick="checkSendOffers();">Send Email</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnAddSub" type="button" onclick="startAddSub(0);">Add Subscription</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnMarkAccept" type="button" onclick="checkMarkAccepted();">Mark as Accepted</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnDelOffer" type="button" onclick="deleteRenewals();">Delete Selected Renewals</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemPmtMthd" type="button" onclick="removePaymentMethods();">Remove Payment Method</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemAddons" type="button" onclick="removeAddOn();">Remove Addons</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnUpdateGraceDate" type="button" onclick="massUpdateGraceEndDate();">Mass Change Grace End Dates</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnRemAddons" type="button" onclick="changeOfferExpirationDate();">Mass Change Offer Expiration Date</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="X">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnRemPmtMthd" type="button" onclick="removePaymentMethods();">Remove Payment Method</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfcase value="D">
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnRemPmtMthd" type="button" onclick="removePaymentMethods();">Remove Payment Method</button>
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfcase>
		<cfdefaultcase>
			<div class="d-flex flex-wrap">
				<button class="btn btn-sm btn-secondary m-2" name="btnExportSubs" type="button" onclick="startExportSubs();">Export</button>
			</div>
		</cfdefaultcase>
	</cfswitch>
</div>

<cfif local.SubReportFilter.listFilter.fSubStatus eq "R">
	<a id="auditT" name="auditT"></a>

	<div class="row my-3">
		<div class="col-xl-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">
						Manage History <cfif ArrayLen(local.subOfferAuditTrail.arrValue) gt 0>(#local.subOfferAuditTrail.totalCount#)</cfif>
					</div>
				</div>
				<div class="card-body p-1">
					<cfif ArrayLen(local.subOfferAuditTrail.arrValue) gt 0>
						<div id="auditResultsShow" class="mt-1 mb-3"<cfif local.auditTrailFlag eq 'all'> style="display:none;"</cfif>>
							<input type="button" id="btnShowHistory" class="btn btn-sm btn-primary" onClick="toggleATGrid()" value="Show History" />
						</div>
						<div id="auditResultsHide" class="my-1 text-right"<cfif local.auditTrailFlag neq 'all'> style="display:none;"</cfif>>
							<input type="button" id="btnHideHistory" class="btn btn-sm btn-secondary" onClick="toggleATGrid()" value="Hide History" /> <cfif local.subOfferAuditTrail.totalCount neq local.subOfferAuditTrail.foundCount><input type="button" id="btnShowAllHistory" class="btn btn-sm btn-primary" onClick="document.location.href='#this.link.listReports#&at=all##auditT'" value="Show All History" /></cfif>
						</div>
						<div id="mcg_gridboxAT"<cfif local.auditTrailFlag neq 'all'> style="display: none;"</cfif>>
							<table class="table table-sm table-striped">
								<thead class="thead-light">
									<tr>
										<th></th>
										<th>Date</th>
										<th>Description</th>
										<th>Updated By</th>
									</tr>
								</thead>
								<cfloop from="1" to="#ArrayLen(local.subOfferAuditTrail.arrValue)#" index="local.atNdx">
									<cfset local.currATItem = local.subOfferAuditTrail.arrValue[local.atNdx]>
									<cfset local.atTS = parsedatetime(local.currATItem["_id__timestamp"])>
									<cfset local.actMemberInfo = application.objMember.getMemberInfo(memberID=local.currATItem.ACTORMEMBERID)>
									
									<tr onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
										<td class="align-top"><cfif IsDefined("local.currATItem.NOTADDED") and arraylen(local.currATItem.NOTADDED)><img id="atTreeImg_#local.currATItem["_id"].ToString()#" src="/assets/common/images/tree-closed.jpg" /></cfif></td>
										<td class="align-top">#DateFormat(local.atTS, "m/d/yyyy")# #TimeFormat(local.atTS, "HH:mm:ss")#</td>
										<td class="align-top">#local.currATItem.MAINMESSAGE#</td>
										<td class="align-top">#RTrim(local.actMemberInfo.firstname & ' ' &local.actMemberInfo.middleName)# #RTrim(local.actMemberInfo.lastName & ' ' & local.actMemberInfo.suffix)#</td>
									</tr>
									<cfif IsDefined("local.currATItem.NOTADDED") and arrayLen(local.currATItem.NOTADDED)>
										<tbody id="atChanges_#local.currATItem["_id"].ToString()#" style="display:none;">
											<tr onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
												<td colspan="2"></td>
												<td colspan="2">
													<cfset local.loopMemberID = 0>
													<cfloop array="#local.currATItem.NOTADDED#" index="local.thisMessage">
														<cfif local.loopMemberID neq local.thisMessage.memberID>
															<cfif local.loopMemberID neq 0><br><br></cfif>
															<a href="#local.editMemberLink#&memberID=#local.thisMessage.memberID#&tab=subscriptions">#local.thisMessage.memberName#</a><br>
															<cfset local.loopMemberID = local.thisMessage.memberID>
														</cfif>
														<cfif IsDefined("local.thisMessage.errMessage")>
															<cfset local.msgToUse = local.thisMessage.errMessage>
														<cfelse>
															<cfset local.msgToUse = local.thisMessage.errType>
														</cfif>
														<cfset local.atMsgLen = Len(local.msgToUse) + 5>
														<cfif local.atMsgLen gt 100>
															<cfset local.firstTimeInLoop = true>
															<cfset local.msgCutoff = 100>
															<cfset local.workingString = local.msgToUse>
															<cfloop condition="(Len(local.workingString) gt 0)">
																<cfif local.firstTimeInLoop>
																	<cfset local.firstTimeInLoop = false>
																	&nbsp;&nbsp; 
																<cfelse>
																	&nbsp;&nbsp;&nbsp;
																</cfif>
																<cfif (Find(" ", local.workingString) neq 0) AND (Len(local.workingString) gt local.msgCutoff)>
																	<cfset local.currSpaceIndex = Left(local.workingString, local.msgCutoff).lastIndexOf(" ")>
																<cfelse>
																	<cfset local.currSpaceIndex = Len(local.workingString)>
																</cfif>
																#Left(local.workingString, local.currSpaceIndex)#
																<cfif (Len(local.workingString)-local.currSpaceIndex) gt 0>
																	<cfset local.workingString = Right(local.workingString, Len(local.workingString)-local.currSpaceIndex)>
																<cfelse>
																	<cfset local.workingString = "">
																</cfif>
															</cfloop>
														
														<cfelse>
														&nbsp;&nbsp;#local.msgToUse#
														</cfif>
													</cfloop>
												</td>
											</tr>
										</tbody>
									</cfif>
								</cfloop>
							</table>
						</div>
					<cfelse>
						<div id="auditResultsDesc">
							No billed history recorded.
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</div>
</cfif>
</cfoutput>