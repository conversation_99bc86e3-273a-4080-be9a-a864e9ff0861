<cfsavecontent variable="local.copyRates">
	<cfoutput>
	<script language="javascript">
		var #ToScript(local.programID,'sw_programid')#;
		var #ToScript(local.copyAction,'copyAction')#;
		var SWProgramsTable;

		function dofilterSWPrograms() {
			SWProgramsTable.draw();
		}
		function selectSWProgram(copypid) {
			if(copyAction == 'copyForSWODProgram'){
				var copyRatesFromProgramResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
							top.loadSWODSubmissionProgramRates(r);
							top.MCModalUtils.hideModal();
					} else { 
						alert('We were unable to copy rates from the selected #lcase(local.programDisplayName)#.');
						$('.btnSelectSWProgram').removeClass('disabled');
					}
				};
				$('.btnSelectSWProgram').addClass('disabled');
				var objParams = { participantID:#local.participantID#, copyFromProgramID:copypid};
				TS_AJX('ADMINSWCOMMON','getSeminarRatesForSWODProgram',objParams,copyRatesFromProgramResult,copyRatesFromProgramResult,10000,copyRatesFromProgramResult);
			} else {
				var copyRatesFromProgramResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						top.reloadSWRatesGrid();
						top.MCModalUtils.hideModal();
					} else { 
						alert('We were unable to copy rates from the selected #lcase(local.programDisplayName)#.');
						$('.btnSelectSWProgram').removeClass('disabled');
					}
				};
				var msg = 'Are you sure you want to copy rates from the selected #lcase(local.programDisplayName)#?';
				if (confirm(msg)) {
					$("##copyRate_"+copypid).html('<i class="fa-solid fa-spinner fa-spin"></i>');
					$('.btnSelectSWProgram').addClass('disabled');
					var objParams = { participantID:#local.participantID#, copyFromProgramID:copypid, copyToProgramID:sw_programid, ft:'#local.ft#' };
					TS_AJX('ADMINSWCOMMON','copyRatesFromSWProgram',objParams,copyRatesFromProgramResult,copyRatesFromProgramResult,10000,copyRatesFromProgramResult);
				}
			}
		}

		function quickFilterSWLPrograms(d) {
			var now = new Date();
			var thisDate = new Date();
			var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

			var n = d * 1;
			if (n >= 0) {
				$('##fDateFrom').val(moment(todayDate).format('M/D/YYYY'));
				todayDate.setDate(now.getDate()+n);
				$('##fDateTo').val(moment(todayDate).format('M/D/YYYY'));
			} else {
				n = d * -1;
				$('##fDateTo').val(moment(todayDate).format('M/D/YYYY'));
				todayDate.setDate(now.getDate()-n);
				$('##fDateFrom').val(moment(todayDate).format('M/D/YYYY'));
			}
			dofilterSWPrograms();
		}
		function initSWProgramsTable(){
			SWProgramsTable = $('##SWProgramsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50 ],
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.programsList#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [ 
					{"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" id="copyRate_'+data.programID+'" class="btn btn-xs btn-outline-primary p-1 btnSelectSWProgram" title="Select '+data.programDisplayText+' To Copy Rates"  class="copyRatesIcon" onclick="selectSWProgram('+data.programID+');return false;"><i class="fa-solid fa-plus"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "5%",
						"className": "text-center align-top",
						"orderable": false
					},
					<cfif local.ft EQ "SWL">
						{ "data": "dateStart", "width": "15%", "className": "text-center align-top" },
					</cfif>
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if (!data.isActive) renderData += '<span class="badge badge-warning">Inactive</span>';
								renderData += '<div>'+data.programName+'</div>';
								if(data.programSubTitle.length) renderData += '<div class="small text-dim">'+data.programSubTitle+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top"
					},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? (data.publisherOrgCode == data.siteCode ? "Publisher" : 'Opt-In') : data;
						},
						"width": "15%",
						"className": "text-center align-top",
						"orderable": false
					}
				],
				"order": [[1, '#local.ft EQ "SWL" ? "desc" : 'asc'#']]
			});
		}
		$(function() {
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupCalendarIcons('frmFilter');
			initSWProgramsTable();			
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.copyRates)#">

<cfoutput>
<div class="p-3">
	<div>We will <b>replace all rates</b> in your #lcase(local.programDisplayName)# with the rates from the selected #lcase(local.programDisplayName)#.</div>
	<form method="POST" name="frmFilter" id="frmFilter" onsubmit="dofilterSWPrograms(); return false;">
	<input type="hidden" name="fPubType" id="fPubType" value="P">
	<cfif local.ft EQ "SWL">
		<div class="form-row my-3">
			<div class="col">
				<div class="card card-box h-100">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-md">
							Quick date filters
						</div>
					</div>
					<div class="card-body pb-3 px-2">
						<div class="form-row">
							<div class="col-auto font-weight-bold">Next:</div>
							<div class="col">
								<a href="javascript:quickFilterSWLPrograms(7);" class="badge badge-neutral-second text-second mr-1">1w</a>
								<a href="javascript:quickFilterSWLPrograms(14);" class="badge badge-neutral-second text-second mr-1">2w</a> 
								<a href="javascript:quickFilterSWLPrograms(21);" class="badge badge-neutral-second text-second mr-1">3w</a> 
								<a href="javascript:quickFilterSWLPrograms(30);" class="badge badge-neutral-second text-second mr-1">1m</a> 
								<a href="javascript:quickFilterSWLPrograms(60);" class="badge badge-neutral-second text-second mr-1">2m</a> 
								<a href="javascript:quickFilterSWLPrograms(90);" class="badge badge-neutral-second text-second mr-1">3m</a> 
								<a href="javascript:quickFilterSWLPrograms(180);" class="badge badge-neutral-second text-second mr-1">6m</a> 
								<a href="javascript:quickFilterSWLPrograms(365);" class="badge badge-neutral-second text-second">1y</a>
							</div>
						</div>
						<div class="form-row mt-1">
							<div class="col-auto font-weight-bold">Last:</div>
							<div class="col">
								<a href="javascript:quickFilterSWLPrograms(-7);" class="badge badge-neutral-second text-second mr-1">1w</a> 
								<a href="javascript:quickFilterSWLPrograms(-14);" class="badge badge-neutral-second text-second mr-1">2w</a> 
								<a href="javascript:quickFilterSWLPrograms(-21);" class="badge badge-neutral-second text-second mr-1">3w</a> 
								<a href="javascript:quickFilterSWLPrograms(-30);" class="badge badge-neutral-second text-second mr-1">1m</a> 
								<a href="javascript:quickFilterSWLPrograms(-60);" class="badge badge-neutral-second text-second mr-1">2m</a> 
								<a href="javascript:quickFilterSWLPrograms(-90);" class="badge badge-neutral-second text-second mr-1">3m</a> 
								<a href="javascript:quickFilterSWLPrograms(-180);" class="badge badge-neutral-second text-second mr-1">6m</a>
								<a href="javascript:quickFilterSWLPrograms(-365);" class="badge badge-neutral-second text-second">1y</a>
							</div>
						</div>
						<div class="form-row mt-3">
							<div class="col">
								<div class="form-label-group">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fDateFrom" id="fDateFrom" value="#local.fDateFrom#" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fDateFrom"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fDateFrom">Date From</label>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-label-group">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fDateTo" id="fDateTo" value="#local.fDateTo#" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fDateTo"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fDateTo">Date To</label>
									</div>
								</div>
							</div>
						</div>
						<div class="mt-2">
							<button type="button" onclick="dofilterSWPrograms()" class="btn btn-sm btn-primary">Show Programs</button>
						</div>
					</div>
				</div>
			</div>
			<div class="col-5">
				<div class="card card-box h-100">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-md">
							Additional filters
						</div>
					</div>
					<div class="card-body p-2">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<input type="text" name="fProgramCode" id="fProgramCode" class="form-control" value="">
								<label for="fProgramCode">Program Code</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fStatus" id="fStatus" class="custom-select">
									<option value="1">Hide Inactive Programs</option>
									<option value="0">Show Inactive Programs</option>
								</select>
								<label for="fStatus">Program Status</label>
							</div>
						</div>
						<div class="mt-2">
							<button type="submit" class="btn btn-sm btn-primary">Show Programs</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	<cfelseif listFindNoCase("SWOD,SWB", local.ft)>
		<div class="form-row my-3">
			<div class="col">
				<div class="form-label-group mb-0">
					<select name="fStatus" id="fStatus" class="custom-select" onchange="dofilterSWPrograms();">
						<option value="1">Hide Inactive #local.programDisplayName#s</option>
						<option value="0">Show Inactive #local.programDisplayName#s</option>
					</select>
					<label for="fStatus">Program Status</label>
				</div>
			</div>
		</div>
	</cfif>
	</form>

	<table id="SWProgramsTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th></th>
				<cfif local.ft eq "SWL">
					<th>Date</th>
				</cfif>
				<th>Program</th>
				<th>Opt-In</th>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>