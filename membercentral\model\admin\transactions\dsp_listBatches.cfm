	<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script language="javascript">
		let listBatchesTable;
		
		function initializeListBatchesTable(){
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			listBatchesTable = $('##listBatchesTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.resultsList#",
					"type": "post",
					"data": function(d) {
						var arrFormData = $('##frmFilter').serializeArray();
						$.each(arrFormData, function() {
							d[this.name] = this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 	"data":null, 
						"width": "15%" ,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<p class="m-0">'+row.depositDate+'</p>';
								renderData += '<p class="m-0">'+row.status+'</p>';
							}
							return type === 'display' ? renderData : data;
						},			
				
					},
					{ 	"data":null, 
						"width": "51%" ,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<p class="m-0">'+row.batchName+'</p>';
								renderData += '<p class="m-0">'+row.profileName+'&nbsp;</p>';
							}
							return type === 'display' ? renderData : data;
						},			
				
					},
					{ 	"data":null, 
						"width": "12%" ,
						"orderable": false, 
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<p class="m-0">'+row.controlAmountFormatted+'</p>';
								renderData += '<p class="m-0">'+row.controlCount+'</p>';
							}
							return type === 'display' ? renderData : data;
						},			
				
					},
					{ 	"data":null, 
						"width": "12%" ,
						"orderable": false,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<p class="m-0">'+row.actualAmountFormatted+'</p>';
								renderData += '<p class="m-0">'+row.actualCount+'</p>';
							}
							return type === 'display' ? renderData : data;
						},	
				
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if(data.canEdit == 1)
									renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="editBatch('+data.batchID+');" title="Edit Batch"><i class="fa-solid fa-pencil"></i></a>';
								else 
									renderData += '<a href="##" class="btn btn-sm btn-outline-light px-2 m-1 text-muted disabled" title="Edit Batch"><i class="fa-solid fa-pencil"></i></a>';

								renderData += '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="viewBatch('+data.batchID+');" title="View Batch Details"><i class="fa-regular fa-file-invoice-dollar"></i></a>';
							}

							return type === 'display' ? renderData : data;
						},
						"width": "10%",
						"className": "text-center",
						"orderable": false
					}
				],
				"order": [[0, 'desc']],
				"searching": false,
				"columnDefs": [ {
					"targets": 2,
					"createdCell": function (td, cellData, row) {
						if ((row.controlAmount != row.actualAmount) || (row.controlCount != row.actualCount)){
							$(td).addClass('text-danger')
						}
					}
				} ]
			});
		}
		function setMultiSelectFilterData(){
			var statusIDText = $('##statusID').val();
			$('##statusList').val(statusIDText);

			var payProfileIDText = $('##payProfileID').val();
			$('##payProfileList').val(payProfileIDText);

			var batchTypeIDText = $('##batchTypeID').val();
			$('##batchTypeList').val(batchTypeIDText);
		}
		function filterBatches() {
			if (!$('##divFilterForm').is(':visible')) {
				$('div.divBatchTool').hide();
				$('##divFilterForm').show();
			}
		}
		function doFilterBatches() {
			setMultiSelectFilterData();			
			listBatchesTable.draw();
			$('##divFilterForm').hide('slow');
		}
		
		function reloadData() { doFilterBatches(); }

		<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>
			function editBatch(bid) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: bid > 0 ? 'Edit Batch' : 'Open Batch',
					iframe: true,
					contenturl: '#this.link.edit#&bid='+bid,
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'batchAddedit',
						extrabuttonlabel: 'Save Batch'
					}
				});
				$('div.divBatchTool').hide();
			}
			function closeEditBatch() { 
				reloadData(); 
				$('##MCModal').modal('hide');
			}
		</cfif>
		function batchAddedit() {
			$('##MCModalBodyIframe')[0].contentWindow._validateBatchForm();
		}
		function viewBatch(bid) {
			window.open('#this.link.view#&bid=' + bid);
		}

		$(function() {
			setMultiSelectFilterData();
			initializeListBatchesTable();
			mca_setupDatePickerRangeFields('depdateStart','depdateEnd');
			mca_setupSelect2();
			mca_setupCalendarIcons('frmFilter');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

<cfoutput>
<h4>Manage Batches</h4>
<div class="mb-2">Batches contain payments and allocations.</div>

<div class="toolButtonBar">
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.batchCreate') is 1>
		<div><a name="btnOpenBatch" <cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>href="javascript:editBatch();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to open a new batch."<cfelse>href="javascript:void(0);" class="text-muted" disabled</cfif>><i class="fa-regular fa-circle-plus"></i> Open Batch</a></div>
	</cfif>
	<div><a href="javascript:filterBatches();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter batches."><i class="fa-regular fa-filter"></i> Filter Batches</a></div>
</div>

<div id="divFilterForm" class="divBatchTool" style="display:none;">
	<div class="row mb-3">
		<div class="col-xl-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Filter Batches
					</div>
				</div>
				<div class="card-body pb-3">
					<form action="#this.link.list#" method="POST" name="frmFilter" id="frmFilter">
						<input type="hidden" name="statusList" id="statusList">
						<input type="hidden" name="payProfileList" id="payProfileList">
						<input type="hidden" name="batchTypeList" id="batchTypeList">
						
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select name="statusID" id="statusID" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
											<cfloop query="local.qryStatus">
												<option value="#local.qryStatus.statusID#" <cfif listfind(arguments.event.getTrimValue('statusID'),local.qryStatus.statusID) >selected</cfif>>#local.qryStatus.status#</option>
											</cfloop>
										</select>
										<label for="statusID">Status</label>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select id="payProfileID" name="payProfileID" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
											<cfloop query="local.qryPayProfiles">
												<option value="#local.qryPayProfiles.profileID#">#local.qryPayProfiles.profileName#</option>
											</cfloop>
										</select>
										<label for="payProfileID">Pay Profile</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select id="batchTypeID" name="batchTypeID" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
											<cfloop query="local.qryTypes">
												<option value="#local.qryTypes.batchTypeID#">#local.qryTypes.batchType#</option>
											</cfloop>
										</select>
										<label for="batchTypeID">Batch Type</label>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<input type="text" name="batchName" id="batchName" value="" class="form-control">
										<label for="batchName">Batch Name Contains...</label>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="depdateStart" id="depdateStart" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="depdateStart"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('depdateStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="depdateStart">Dep Date From</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="depdateEnd" id="depdateEnd" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="depdateEnd"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('depdateEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="depdateEnd">Dep Date To</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="batchAmtStart" id="batchAmtStart" value="" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="batchAmtStart">Batch Amt From</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="batchAmtEnd" id="batchAmtEnd" value="" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
												<label for="batchAmtEnd">Batch Amt To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12 text-right">
								<button type="button" name="btnFilterGrid" class="btn btn-sm btn-primary" onclick="doFilterBatches();">
									<i class="fa-light fa-filter"></i> Filter Batches
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
	
<div id="mcg_rnum" style="display:none;"></div>
<div class="my-2">
	<table id="listBatchesTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Date</th>
				<th>Batch Name / Payment Profile</th>
				<th>Control</th>
				<th>Actual</th>
				<th>Tools</th>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>